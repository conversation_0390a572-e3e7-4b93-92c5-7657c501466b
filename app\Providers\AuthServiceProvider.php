<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // 注册用户组相关Policy
        \Gate::define('manage-user-group', function ($user) {
            // owner和manager有权限，hod需分配权限
            if ($user->hasRole('owner') || $user->hasRole('manager')) {
                return true;
            }
            // hod需分配权限
            return $user->can('manage user group');
        });
        \Gate::define('manage-group-member', function ($user) {
            if ($user->hasRole('owner') || $user->hasRole('manager')) {
                return true;
            }
            return $user->can('manage group member');
        });
    }
}
