<?php

namespace App\Http\Controllers;

use App\Models\Skill;
use Illuminate\Http\Request;

class SkillController extends Controller
{

    public function index()
    {
        if(\Auth::user()->can('manage skill'))
        {
            $skills = Skill::where('parent_id',parentId())->get();
            return view('skill.index', compact('skills'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        return view('skill.create');
    }


    public function store(Request $request)
    {
        if(\Auth::user()->can('create skill'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'skill' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $skill            = new Skill();
            $skill->skill     = $request->skill;
            $skill->parent_id = parentId();
            $skill->save();
            return redirect()->back()->with('success', __('Skill successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function show(Skill $skill)
    {
        //
    }


    public function edit(Skill $skill)
    {
        return view('skill.edit', compact('skill'));
    }


    public function update(Request $request, Skill $skill)
    {
        if(\Auth::user()->can('edit skill'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'skill' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $skill->skill = $request->skill;
            $skill->save();
            return redirect()->back()->with('success', __('Skill successfully updated.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function destroy(Skill $skill)
    {
        if(\Auth::user()->can('delete skill'))
        {
            $skill->delete();
            return redirect()->back()->with('success', 'Skill successfully deleted.');
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }
}
