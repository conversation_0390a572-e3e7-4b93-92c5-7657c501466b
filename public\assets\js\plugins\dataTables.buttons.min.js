/*! Buttons for DataTables 3.0.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(e){var o,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return e(t,window,document)}):"object"==typeof exports?(o=require("jquery"),i=function(t,n){n.fn.dataTable||require("datatables.net")(t,n)},"undefined"==typeof window?module.exports=function(t,n){return t=t||window,n=n||o(t),i(t,n),e(n,t,t.document)}:(i(window,o),module.exports=e(o,window,window.document))):e(jQuery,window,document)}(function(x,g,m){"use strict";var e=x.fn.dataTable,o=0,C=0,w=e.ext.buttons,i=null;function v(t,n,e){x.fn.animate?t.stop().fadeIn(n,e):(t.css("display","block"),e&&e.call(t))}function y(t,n,e){x.fn.animate?t.stop().fadeOut(n,e):(t.css("display","none"),e&&e.call(t))}function _(n,t){if(!e.versionCheck("2"))throw"Warning: Buttons requires DataTables 2 or newer";if(!(this instanceof _))return function(t){return new _(t,n).container()};!0===(t=void 0===t?{}:t)&&(t={}),Array.isArray(t)&&(t={buttons:t}),this.c=x.extend(!0,{},_.defaults,t),t.buttons&&(this.c.buttons=t.buttons),this.s={dt:new e.Api(n),buttons:[],listenKeys:"",namespace:"dtb"+o++},this.dom={container:x("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)},this._constructor()}x.extend(_.prototype,{action:function(t,n){t=this._nodeToButton(t);return void 0===n?t.conf.action:(t.conf.action=n,this)},active:function(t,n){var t=this._nodeToButton(t),e=this.c.dom.button.active,o=x(t.node);return t.inCollection&&this.c.dom.collection.button&&void 0!==this.c.dom.collection.button.active&&(e=this.c.dom.collection.button.active),void 0===n?o.hasClass(e):(o.toggleClass(e,void 0===n||n),this)},add:function(t,n,e){var o=this.s.buttons;if("string"==typeof n){for(var i=n.split("-"),s=this.s,a=0,r=i.length-1;a<r;a++)s=s.buttons[+i[a]];o=s.buttons,n=+i[i.length-1]}return this._expandButton(o,t,void 0!==t?t.split:void 0,(void 0===t||void 0===t.split||0===t.split.length)&&void 0!==s,!1,n),void 0!==e&&!0!==e||this._draw(),this},collectionRebuild:function(t,n){var e=this._nodeToButton(t);if(void 0!==n){for(var o=e.buttons.length-1;0<=o;o--)this.remove(e.buttons[o].node);for(e.conf.prefixButtons&&n.unshift.apply(n,e.conf.prefixButtons),e.conf.postfixButtons&&n.push.apply(n,e.conf.postfixButtons),o=0;o<n.length;o++){var i=n[o];this._expandButton(e.buttons,i,void 0!==i&&void 0!==i.config&&void 0!==i.config.split,!0,void 0!==i.parentConf&&void 0!==i.parentConf.split,null,i.parentConf)}}this._draw(e.collection,e.buttons)},container:function(){return this.dom.container},disable:function(t){t=this._nodeToButton(t);return x(t.node).addClass(this.c.dom.button.disabled).prop("disabled",!0),this},destroy:function(){x("body").off("keyup."+this.s.namespace);for(var t=this.s.buttons.slice(),n=0,e=t.length;n<e;n++)this.remove(t[n].node);this.dom.container.remove();var o=this.s.dt.settings()[0];for(n=0,e=o.length;n<e;n++)if(o.inst===this){o.splice(n,1);break}return this},enable:function(t,n){return!1===n?this.disable(t):(n=this._nodeToButton(t),x(n.node).removeClass(this.c.dom.button.disabled).prop("disabled",!1),this)},index:function(t,n,e){n||(n="",e=this.s.buttons);for(var o=0,i=e.length;o<i;o++){var s=e[o].buttons;if(e[o].node===t)return n+o;if(s&&s.length){s=this.index(t,o+"-",s);if(null!==s)return s}}return null},name:function(){return this.c.name},node:function(t){return t?(t=this._nodeToButton(t),x(t.node)):this.dom.container},processing:function(t,n){var e=this.s.dt,o=this._nodeToButton(t);return void 0===n?x(o.node).hasClass("processing"):(x(o.node).toggleClass("processing",n),x(e.table().node()).triggerHandler("buttons-processing.dt",[n,e.button(t),e,x(t),o.conf]),this)},remove:function(t){var n=this._nodeToButton(t),e=this._nodeToHost(t),o=this.s.dt;if(n.buttons.length)for(var i=n.buttons.length-1;0<=i;i--)this.remove(n.buttons[i].node);n.conf.destroying=!0,n.conf.destroy&&n.conf.destroy.call(o.button(t),o,x(t),n.conf),this._removeKey(n.conf),x(n.node).remove();o=x.inArray(n,e);return e.splice(o,1),this},text:function(t,n){function e(t){return"function"==typeof t?t(i,s,o.conf):t}var o=this._nodeToButton(t),t=o.textNode,i=this.s.dt,s=x(o.node);return void 0===n?e(o.conf.text):(o.conf.text=n,t.html(e(n)),this)},_constructor:function(){var e=this,t=this.s.dt,o=t.settings()[0],n=this.c.buttons;o._buttons||(o._buttons=[]),o._buttons.push({inst:this,name:this.c.name});for(var i=0,s=n.length;i<s;i++)this.add(n[i]);t.on("destroy",function(t,n){n===o&&e.destroy()}),x("body").on("keyup."+this.s.namespace,function(t){var n;m.activeElement&&m.activeElement!==m.body||(n=String.fromCharCode(t.keyCode).toLowerCase(),-1!==e.s.listenKeys.toLowerCase().indexOf(n)&&e._keypress(n,t))})},_addKey:function(t){t.key&&(this.s.listenKeys+=(x.isPlainObject(t.key)?t.key:t).key)},_draw:function(t,n){t||(t=this.dom.container,n=this.s.buttons),t.children().detach();for(var e=0,o=n.length;e<o;e++)t.append(n[e].inserter),t.append(" "),n[e].buttons&&n[e].buttons.length&&this._draw(n[e].collection,n[e].buttons)},_expandButton:function(t,n,e,o,i,s,a){for(var r,l=this.s.dt,c=this.c.dom.collection,u=Array.isArray(n)?n:[n],d=0,f=(u=void 0===n?Array.isArray(e)?e:[e]:u).length;d<f;d++){var p=this._resolveExtends(u[d]);if(p)if(r=!(!p.config||!p.config.split),Array.isArray(p))this._expandButton(t,p,void 0!==h&&void 0!==h.conf?h.conf.split:void 0,o,void 0!==a&&void 0!==a.split,s,a);else{var h=this._buildButton(p,o,void 0!==p.split||void 0!==p.config&&void 0!==p.config.split,i);if(h){if(null!=s?(t.splice(s,0,h),s++):t.push(h),h.conf.buttons&&(h.collection=x("<"+c.container.content.tag+"/>"),h.conf._collection=h.collection,x(h.node).append(c.action.dropHtml),this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!r,r,s,h.conf)),h.conf.split){h.collection=x("<"+c.container.tag+"/>"),h.conf._collection=h.collection;for(var b=0;b<h.conf.split.length;b++){var g=h.conf.split[b];"object"==typeof g&&(g.parent=a,void 0===g.collectionLayout&&(g.collectionLayout=h.conf.collectionLayout),void 0===g.dropup&&(g.dropup=h.conf.dropup),void 0===g.fade)&&(g.fade=h.conf.fade)}this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!r,r,s,h.conf)}h.conf.parent=a,p.init&&p.init.call(l.button(h.node),l,x(h.node),p)}}}},_buildButton:function(n,t,e,o){function i(t){return"function"==typeof t?t(f,c,n):t}var s,a,r,l,c,u=this,d=this.c.dom,f=this.s.dt,p=x.extend(!0,{},d.button);if(t&&e&&d.collection.split?x.extend(!0,p,d.collection.split.action):o||t?x.extend(!0,p,d.collection.button):e&&x.extend(!0,p,d.split.button),n.spacer)return d=x("<"+p.spacer.tag+"/>").addClass("dt-button-spacer "+n.style+" "+p.spacer.className).html(i(n.text)),{conf:n,node:d,inserter:d,buttons:[],inCollection:t,isSplit:e,collection:null,textNode:d};if(n.available&&!n.available(f,n)&&!n.html)return!1;n.html?c=x(n.html):(a=function(t,n,e,o,i){o.action.call(n.button(e),t,n,e,o,i),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o])},r=function(t,n,e,o){o.async?(u.processing(e[0],!0),setTimeout(function(){a(t,n,e,o,function(){u.processing(e[0],!1)})},o.async)):a(t,n,e,o,function(){})},d=n.tag||p.tag,l=void 0===n.clickBlurs||n.clickBlurs,c=x("<"+d+"/>").addClass(p.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(t){t.preventDefault(),!c.hasClass(p.disabled)&&n.action&&r(t,f,c,n),l&&c.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),!c.hasClass(p.disabled))&&n.action&&r(t,f,c,n)}),"a"===d.toLowerCase()&&c.attr("href","#"),"button"===d.toLowerCase()&&c.attr("type","button"),s=p.liner.tag?(d=x("<"+p.liner.tag+"/>").html(i(n.text)).addClass(p.liner.className),"a"===p.liner.tag.toLowerCase()&&d.attr("href","#"),c.append(d),d):(c.html(i(n.text)),c),!1===n.enabled&&c.addClass(p.disabled),n.className&&c.addClass(n.className),n.titleAttr&&c.attr("title",i(n.titleAttr)),n.attr&&c.attr(n.attr),n.namespace||(n.namespace=".dt-button-"+C++),void 0!==n.config&&n.config.split&&(n.split=n.config.split));var h,b,g,m,v,y,d=this.c.dom.buttonContainer,d=d&&d.tag?x("<"+d.tag+"/>").addClass(d.className).append(c):c;return this._addKey(n),this.c.buttonCreated&&(d=this.c.buttonCreated(n,d)),e&&(b=(h=t?x.extend(!0,this.c.dom.split,this.c.dom.collection.split):this.c.dom.split).wrapper,g=x("<"+b.tag+"/>").addClass(b.className).append(c),m=x.extend(n,{align:h.dropdown.align,attr:{"aria-haspopup":"dialog","aria-expanded":!1},className:h.dropdown.className,closeButton:!1,splitAlignClass:h.dropdown.splitAlignClass,text:h.dropdown.text}),this._addKey(m),v=function(t,n,e,o){w.split.action.call(n.button(g),t,n,e,o),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o]),e.attr("aria-expanded",!0)},y=x('<button class="'+h.dropdown.className+' dt-button"></button>').html(h.dropdown.dropHtml).on("click.dtb",function(t){t.preventDefault(),t.stopPropagation(),y.hasClass(p.disabled)||v(t,f,y,m),l&&y.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),y.hasClass(p.disabled)||v(t,f,y,m))}),0===n.split.length&&y.addClass("dtb-hide-drop"),g.append(y).attr(m.attr)),{conf:n,node:(e?g:c).get(0),inserter:e?g:d,buttons:[],inCollection:t,isSplit:e,inSplit:o,collection:null,textNode:s}},_nodeToButton:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n[e];if(n[e].buttons.length){var i=this._nodeToButton(t,n[e].buttons);if(i)return i}}},_nodeToHost:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n;if(n[e].buttons.length){var i=this._nodeToHost(t,n[e].buttons);if(i)return i}}},_keypress:function(s,a){var r;a._buttonsHandled||(r=function(t){for(var n,e,o=0,i=t.length;o<i;o++)n=t[o].conf,e=t[o].node,!n.key||n.key!==s&&(!x.isPlainObject(n.key)||n.key.key!==s||n.key.shiftKey&&!a.shiftKey||n.key.altKey&&!a.altKey||n.key.ctrlKey&&!a.ctrlKey||n.key.metaKey&&!a.metaKey)||(a._buttonsHandled=!0,x(e).click()),t[o].buttons.length&&r(t[o].buttons)})(this.s.buttons)},_removeKey:function(t){var n;t.key&&(t=(x.isPlainObject(t.key)?t.key:t).key,n=this.s.listenKeys.split(""),t=x.inArray(t,n),n.splice(t,1),this.s.listenKeys=n.join(""))},_resolveExtends:function(e){function t(t){for(var n=0;!x.isPlainObject(t)&&!Array.isArray(t);){if(void 0===t)return;if("function"==typeof t){if(!(t=t.call(i,s,e)))return!1}else if("string"==typeof t){if(!w[t])return{html:t};t=w[t]}if(30<++n)throw"Buttons: Too many iterations"}return Array.isArray(t)?t:x.extend({},t)}var n,o,i=this,s=this.s.dt;for(e=t(e);e&&e.extend;){if(!w[e.extend])throw"Cannot extend unknown button type: "+e.extend;var a=t(w[e.extend]);if(Array.isArray(a))return a;if(!a)return!1;var r=a.className;void 0!==e.config&&void 0!==a.config&&(e.config=x.extend({},a.config,e.config)),e=x.extend({},a,e),r&&e.className!==r&&(e.className=r+" "+e.className),e.extend=a.extend}var l=e.postfixButtons;if(l)for(e.buttons||(e.buttons=[]),n=0,o=l.length;n<o;n++)e.buttons.push(l[n]);var c=e.prefixButtons;if(c)for(e.buttons||(e.buttons=[]),n=0,o=c.length;n<o;n++)e.buttons.splice(n,0,c[n]);return e},_popover:function(o,t,n){function i(){f=!0,y(x(h),p.fade,function(){x(this).detach()}),x(u.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes()).attr("aria-expanded","false"),x("div.dt-button-background").off("click.dtb-collection"),_.background(!1,p.backgroundClassName,p.fade,b),x(g).off("resize.resize.dtb-collection"),x("body").off(".dtb-collection"),u.off("buttons-action.b-internal"),u.off("destroy")}var e,s,a,r,l,c,u=t,d=this.c,f=!1,p=x.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",closeButton:!0,containerClassName:d.dom.collection.container.className,contentClassName:d.dom.collection.container.content.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,popoverTitle:"",rightAlignClassName:"dt-button-right",tag:d.dom.collection.container.tag},n),h=p.tag+"."+p.containerClassName.replace(/ /g,"."),b=t.node();!1===o?i():((d=x(u.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes())).length&&(b.closest(h).length&&(b=d.eq(0)),i()),n=x(".dt-button",o).length,d="",3===n?d="dtb-b3":2===n?d="dtb-b2":1===n&&(d="dtb-b1"),e=x("<"+p.tag+"/>").addClass(p.containerClassName).addClass(p.collectionLayout).addClass(p.splitAlignClass).addClass(d).css("display","none").attr({"aria-modal":!0,role:"dialog"}),o=x(o).addClass(p.contentClassName).attr("role","menu").appendTo(e),b.attr("aria-expanded","true"),b.parents("body")[0]!==m.body&&(b=m.body.lastChild),p.popoverTitle?e.prepend('<div class="dt-button-collection-title">'+p.popoverTitle+"</div>"):p.collectionTitle&&e.prepend('<div class="dt-button-collection-title">'+p.collectionTitle+"</div>"),p.closeButton&&e.prepend('<div class="dtb-popover-close">&times;</div>').addClass("dtb-collection-closeable"),v(e.insertAfter(b),p.fade),n=x(t.table().container()),d=e.css("position"),"container"!==p.span&&"dt-container"!==p.align||(b=b.parent(),e.css("width",n.width())),"absolute"===d?(t=x(b[0].offsetParent),n=b.position(),d=b.offset(),r=t.offset(),s=t.position(),a=g.getComputedStyle(t[0]),r.height=t.outerHeight(),r.width=t.width()+parseFloat(a.paddingLeft),r.right=r.left+r.width,r.bottom=r.top+r.height,t=n.top+b.outerHeight(),r=n.left,e.css({top:t,left:r}),a=g.getComputedStyle(e[0]),(l=e.offset()).height=e.outerHeight(),l.width=e.outerWidth(),l.right=l.left+l.width,l.bottom=l.top+l.height,l.marginTop=parseFloat(a.marginTop),l.marginBottom=parseFloat(a.marginBottom),p.dropup&&(t=n.top-l.height-l.marginTop-l.marginBottom),"button-right"!==p.align&&!e.hasClass(p.rightAlignClassName)||(r=n.left-l.width+b.outerWidth()),"dt-container"!==p.align&&"container"!==p.align||r<n.left&&(r=-n.left),s.left+r+l.width>x(g).width()&&(r=x(g).width()-l.width-s.left),d.left+r<0&&(r=-d.left),s.top+t+l.height>x(g).height()+x(g).scrollTop()&&(t=n.top-l.height-l.marginTop-l.marginBottom),s.top+t<x(g).scrollTop()&&(t=n.top+b.outerHeight()),e.css({top:t,left:r})):((c=function(){var t=x(g).height()/2,n=e.height()/2;e.css("marginTop",-1*(n=t<n?t:n))})(),x(g).on("resize.dtb-collection",function(){c()})),p.background&&_.background(!0,p.backgroundClassName,p.fade,p.backgroundHost||b),x("div.dt-button-background").on("click.dtb-collection",function(){}),p.autoClose&&setTimeout(function(){u.on("buttons-action.b-internal",function(t,n,e,o){o[0]!==b[0]&&i()})},0),x(e).trigger("buttons-popover.dt"),u.on("destroy",i),setTimeout(function(){f=!1,x("body").on("click.dtb-collection",function(t){var n,e;!f&&(n=x.fn.addBack?"addBack":"andSelf",e=x(t.target).parent()[0],!x(t.target).parents()[n]().filter(o).length&&!x(e).hasClass("dt-buttons")||x(t.target).hasClass("dt-button-background"))&&i()}).on("keyup.dtb-collection",function(t){27===t.keyCode&&i()}).on("keydown.dtb-collection",function(t){var n=x("a, button",o),e=m.activeElement;9===t.keyCode&&(-1===n.index(e)?(n.first().focus(),t.preventDefault()):t.shiftKey?e===n[0]&&(n.last().focus(),t.preventDefault()):e===n.last()[0]&&(n.first().focus(),t.preventDefault()))})},0))}}),_.background=function(t,n,e,o){void 0===e&&(e=400),o=o||m.body,t?v(x("<div/>").addClass(n).css("display","none").insertAfter(o),e):y(x("div."+n),e,function(){x(this).removeClass(n).remove()})},_.instanceSelector=function(t,i){var s,a,r;return null==t?x.map(i,function(t){return t.inst}):(s=[],a=x.map(i,function(t){return t.name}),(r=function(t){var n;if(Array.isArray(t))for(var e=0,o=t.length;e<o;e++)r(t[e]);else"string"==typeof t?-1!==t.indexOf(",")?r(t.split(",")):-1!==(n=x.inArray(t.trim(),a))&&s.push(i[n].inst):"number"==typeof t?s.push(i[t].inst):"object"==typeof t&&s.push(t)})(t),s)},_.buttonSelector=function(t,n){for(var c=[],u=function(t,n,e){for(var o,i,s=0,a=n.length;s<a;s++)(o=n[s])&&(t.push({node:o.node,name:o.conf.name,idx:i=void 0!==e?e+s:s+""}),o.buttons)&&u(t,o.buttons,i+"-")},d=function(t,n){var e=[],o=(u(e,n.s.buttons),x.map(e,function(t){return t.node}));if(Array.isArray(t)||t instanceof x)for(s=0,a=t.length;s<a;s++)d(t[s],n);else if(null==t||"*"===t)for(s=0,a=e.length;s<a;s++)c.push({inst:n,node:e[s].node});else if("number"==typeof t)n.s.buttons[t]&&c.push({inst:n,node:n.s.buttons[t].node});else if("string"==typeof t)if(-1!==t.indexOf(","))for(var i=t.split(","),s=0,a=i.length;s<a;s++)d(i[s].trim(),n);else if(t.match(/^\d+(\-\d+)*$/)){var r=x.map(e,function(t){return t.idx});c.push({inst:n,node:e[x.inArray(t,r)].node})}else if(-1!==t.indexOf(":name")){var l=t.replace(":name","");for(s=0,a=e.length;s<a;s++)e[s].name===l&&c.push({inst:n,node:e[s].node})}else x(o).filter(t).each(function(){c.push({inst:n,node:this})});else"object"==typeof t&&t.nodeName&&-1!==(r=x.inArray(t,o))&&c.push({inst:n,node:o[r]})},e=0,o=t.length;e<o;e++){var i=t[e];d(n,i)}return c},_.stripData=function(t,n){return t="string"==typeof t&&(t=(t=t.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")).replace(/<!\-\-.*?\-\->/g,""),n&&!n.stripHtml||(t=t.replace(/<[^>]*>/g,"")),n&&!n.trim||(t=t.replace(/^\s+|\s+$/g,"")),n&&!n.stripNewlines||(t=t.replace(/\n/g," ")),!n||n.decodeEntities)?i?i(t):(c.innerHTML=t,c.value):t},_.entityDecoder=function(t){i=t},_.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{action:{dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>'},container:{className:"dt-button-collection",content:{className:"",tag:"div"},tag:"div"}},button:{tag:"button",className:"dt-button",active:"dt-button-active",disabled:"disabled",spacer:{className:"dt-button-spacer",tag:"span"},liner:{tag:"span",className:""}},split:{action:{className:"dt-button-split-drop-button dt-button",tag:"button"},dropdown:{align:"split-right",className:"dt-button-split-drop",dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>',splitAlignClass:"dt-button-split-left",tag:"button"},wrapper:{className:"dt-button-split",tag:"div"}}}},x.extend(w,{collection:{text:function(t){return t.i18n("buttons.collection","Collection")},className:"buttons-collection",closeButton:!(_.version="3.0.0"),init:function(t,n){n.attr("aria-expanded",!1)},action:function(t,n,e,o){o._collection.parents("body").length?this.popover(!1,o):this.popover(o._collection,o),"keypress"===t.type&&x("a, button",o._collection).eq(0).focus()},attr:{"aria-haspopup":"dialog"}},split:{text:function(t){return t.i18n("buttons.split","Split")},className:"buttons-split",closeButton:!1,init:function(t,n){return n.attr("aria-expanded",!1)},action:function(t,n,e,o){this.popover(o._collection,o)},attr:{"aria-haspopup":"dialog"}},copy:function(){if(w.copyHtml5)return"copyHtml5"},csv:function(t,n){if(w.csvHtml5&&w.csvHtml5.available(t,n))return"csvHtml5"},excel:function(t,n){if(w.excelHtml5&&w.excelHtml5.available(t,n))return"excelHtml5"},pdf:function(t,n){if(w.pdfHtml5&&w.pdfHtml5.available(t,n))return"pdfHtml5"},pageLength:function(t){var n=t.settings()[0].aLengthMenu,e=[],o=[];if(Array.isArray(n[0]))e=n[0],o=n[1];else for(var i=0;i<n.length;i++){var s=n[i];x.isPlainObject(s)?(e.push(s.value),o.push(s.label)):(e.push(s),o.push(s))}return{extend:"collection",text:function(t){return t.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},t.page.len())},className:"buttons-page-length",autoClose:!0,buttons:x.map(e,function(s,t){return{text:o[t],className:"button-page-length",action:function(t,n){n.page.len(s).draw()},init:function(t,n,e){function o(){i.active(t.page.len()===s)}var i=this;t.on("length.dt"+e.namespace,o),o()},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}}),init:function(t,n,e){var o=this;t.on("length.dt"+e.namespace,function(){o.text(e.text)})},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}},spacer:{style:"empty",spacer:!0,text:function(t){return t.i18n("buttons.spacer","")}}}),e.Api.register("buttons()",function(n,e){void 0===e&&(e=n,n=void 0),this.selector.buttonGroup=n;var t=this.iterator(!0,"table",function(t){if(t._buttons)return _.buttonSelector(_.instanceSelector(n,t._buttons),e)},!0);return t._groupSelector=n,t}),e.Api.register("button()",function(t,n){t=this.buttons(t,n);return 1<t.length&&t.splice(1,t.length),t}),e.Api.registerPlural("buttons().active()","button().active()",function(n){return void 0===n?this.map(function(t){return t.inst.active(t.node)}):this.each(function(t){t.inst.active(t.node,n)})}),e.Api.registerPlural("buttons().action()","button().action()",function(n){return void 0===n?this.map(function(t){return t.inst.action(t.node)}):this.each(function(t){t.inst.action(t.node,n)})}),e.Api.registerPlural("buttons().collectionRebuild()","button().collectionRebuild()",function(e){return this.each(function(t){for(var n=0;n<e.length;n++)"object"==typeof e[n]&&(e[n].parentConf=t);t.inst.collectionRebuild(t.node,e)})}),e.Api.register(["buttons().enable()","button().enable()"],function(n){return this.each(function(t){t.inst.enable(t.node,n)})}),e.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(t){t.inst.disable(t.node)})}),e.Api.register("button().index()",function(){var n=null;return this.each(function(t){t=t.inst.index(t.node);null!==t&&(n=t)}),n}),e.Api.registerPlural("buttons().nodes()","button().node()",function(){var n=x();return x(this.each(function(t){n=n.add(t.inst.node(t.node))})),n}),e.Api.registerPlural("buttons().processing()","button().processing()",function(n){return void 0===n?this.map(function(t){return t.inst.processing(t.node)}):this.each(function(t){t.inst.processing(t.node,n)})}),e.Api.registerPlural("buttons().text()","button().text()",function(n){return void 0===n?this.map(function(t){return t.inst.text(t.node)}):this.each(function(t){t.inst.text(t.node,n)})}),e.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(t){t.inst.node(t.node).trigger("click")})}),e.Api.register("button().popover()",function(n,e){return this.map(function(t){return t.inst._popover(n,this.button(this[0].node),e)})}),e.Api.register("buttons().containers()",function(){var i=x(),s=this._groupSelector;return this.iterator(!0,"table",function(t){if(t._buttons)for(var n=_.instanceSelector(s,t._buttons),e=0,o=n.length;e<o;e++)i=i.add(n[e].container())}),i}),e.Api.register("buttons().container()",function(){return this.containers().eq(0)}),e.Api.register("button().add()",function(t,n,e){var o=this.context;return o.length&&(o=_.instanceSelector(this._groupSelector,o[0]._buttons)).length&&o[0].add(n,t,e),this.button(this._groupSelector,t)}),e.Api.register("buttons().destroy()",function(){return this.pluck("inst").unique().each(function(t){t.destroy()}),this}),e.Api.registerPlural("buttons().remove()","buttons().remove()",function(){return this.each(function(t){t.inst.remove(t.node)}),this}),e.Api.register("buttons.info()",function(t,n,e){var o=this;return!1===t?(this.off("destroy.btn-info"),y(x("#datatables_buttons_info"),400,function(){x(this).remove()}),clearTimeout(s),s=null):(s&&clearTimeout(s),x("#datatables_buttons_info").length&&x("#datatables_buttons_info").remove(),t=t?"<h2>"+t+"</h2>":"",v(x('<div id="datatables_buttons_info" class="dt-button-info"/>').html(t).append(x("<div/>")["string"==typeof n?"html":"append"](n)).css("display","none").appendTo("body")),void 0!==e&&0!==e&&(s=setTimeout(function(){o.buttons.info(!1)},e)),this.on("destroy.btn-info",function(){o.buttons.info(!1)})),this}),e.Api.register("buttons.exportData()",function(t){if(this.context.length)return u(new e.Api(this.context[0]),t)}),e.Api.register("buttons.exportInfo()",function(t){return{filename:n(t=t||{},this),title:r(t,this),messageTop:l(this,t,t.message||t.messageTop,"top"),messageBottom:l(this,t,t.messageBottom,"bottom")}});var s,n=function(t,n){var e;return null==(e="function"==typeof(e="*"===t.filename&&"*"!==t.title&&void 0!==t.title&&null!==t.title&&""!==t.title?t.title:t.filename)?e(t,n):e)?null:(e=(e=-1!==e.indexOf("*")?e.replace("*",x("head > title").text()).trim():e).replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""))+(a(t.extension,t,n)||"")},a=function(t,n,e){return null==t?null:"function"==typeof t?t(n,e):t},r=function(t,n){t=a(t.title,t,n);return null===t?null:-1!==t.indexOf("*")?t.replace("*",x("head > title").text()||"Exported data"):t},l=function(t,n,e,o){e=a(e,n,t);return null===e?null:(n=x("caption",t.table().container()).eq(0),"*"===e?n.css("caption-side")!==o?null:n.length?n.text():"":e)},c=x("<textarea/>")[0],u=function(e,t){for(var o=x.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(t){return _.stripData(t,o)},footer:function(t){return _.stripData(t,o)},body:function(t){return _.stripData(t,o)}},customizeData:null},t),t=e.columns(o.columns).indexes().map(function(t){var n=e.column(t);return o.format.header(n.title(),t,n.header())}).toArray(),n=e.table().footer()?e.columns(o.columns).indexes().map(function(t){var n=e.column(t).footer();return o.format.footer(n?x(".dt-column-title",n).html():"",t,n)}).toArray():null,i=x.extend({},o.modifier),s=(e.select&&"function"==typeof e.select.info&&void 0===i.selected&&e.rows(o.rows,x.extend({selected:!0},i)).any()&&x.extend(i,{selected:!0}),e.rows(o.rows,i).indexes().toArray()),s=e.cells(s,o.columns,{order:i.order}),a=s.render(o.orthogonal).toArray(),r=s.nodes().toArray(),l=s.indexes().toArray(),c=t.length,u=[],d=0,f=0,p=0<c?a.length/c:0;f<p;f++){for(var h=[c],b=0;b<c;b++)h[b]=o.format.body(a[d],l[f+b].row,l[f+b].column,r[d]),d++;u[f]=h}i={header:t,headerStructure:e.table().header.structure(o.columns),footer:n,footerStructure:e.table().footer.structure(o.columns),body:u};return o.customizeData&&o.customizeData(i),i};function t(t,n){t=new e.Api(t),n=n||t.init().buttons||e.defaults.buttons;return new _(t,n).container()}return x.fn.dataTable.Buttons=_,x.fn.DataTable.Buttons=_,x(m).on("init.dt plugin-init.dt",function(t,n){"dt"===t.namespace&&(t=n.oInit.buttons||e.defaults.buttons)&&!n._buttons&&new _(n,t).container()}),e.ext.feature.push({fnInit:t,cFeature:"B"}),e.feature&&e.feature.register("buttons",t),e});