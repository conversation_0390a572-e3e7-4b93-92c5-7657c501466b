<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Interview extends Model
{
    use HasFactory;
    protected $fillable = [
        'interview_date',
        'interview_start_time',
        'interview_end_time',
        'total_duration',
        'applicant',
        'assign_user',
        'notes',
        'parent_id',
    ];

    public function applicants()
    {
        return $this->belongsTo('App\Models\Applicant','applicant','id');
    }

    public function users()
    {
        return $this->belongsTo('App\Models\User', 'assign_user', 'id');
    }
}
