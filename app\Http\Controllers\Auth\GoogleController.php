<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;

class GoogleController extends Controller
{
    /**
     * 跳转到 Google 认证页面
     */
    public function redirectToGoogle()
    {
        // 跳转到 Google OAuth 认证
        return Socialite::driver('google')->redirect();
    }

    /**
     * Google 认证回调处理
     */
    public function handleGoogleCallback()
    {
        try {
            // 获取 Google 用户信息
            $googleUser = Socialite::driver('google')->stateless()->user();
            $email = $googleUser->getEmail();
            $googleId = $googleUser->getId();
            $name = $googleUser->getName();

            // 检查本地用户表是否有该邮箱
            $user = User::where('email', $email)->first();
            if (!$user) {
                // 没有该邮箱，禁止登录，提示联系管理员
                return redirect()->route('login')->with('error', '该邮箱未在系统中注册，请联系管理员创建账号。');
            }

            // 检查账户是否被锁定
            if ($user->isLocked()) {
                return redirect()->route('login')->with('error', '账户已被锁定，请联系管理员或经理解锁。');
            }

            // 绑定 google_id（如未绑定）
            if (empty($user->google_id)) {
                $user->google_id = $googleId;
                $user->save();
            }

            // 登录用户
            Auth::login($user);
            Session::regenerate();

            // 跳转到首页或后台
            return redirect()->intended('/');
        } catch (\Exception $e) {
            Log::error('Google 登录失败: ' . $e->getMessage());
            return redirect()->route('login')->with('error', 'Google 登录失败，请重试。');
        }
    }

    /**
     * 跳转到 Google 认证页面（个人绑定）
     */
    public function bindGoogle()
    {
        // 跳转到 Google OAuth 认证，state 用于防 CSRF
        return Socialite::driver('google')->with(['prompt' => 'select_account'])->redirect();
    }

    /**
     * Google 认证回调处理（个人绑定）
     */
    public function bindGoogleCallback()
    {
        try {
            // 获取 Google 用户信息
            $googleUser = Socialite::driver('google')->stateless()->user();
            $googleId = $googleUser->getId();
            $googleEmail = $googleUser->getEmail();

            $user = Auth::user();
            // 绑定 google_id 和 google 邮箱
            $user->google_id = $googleId;
            // 如有 google_email 字段可一并保存
            // $user->google_email = $googleEmail;
            $user->save();

            return redirect()->route('setting.index')->with('success', 'Google 账号绑定成功！');
        } catch (\Exception $e) {
            Log::error('Google 绑定失败: ' . $e->getMessage());
            return redirect()->route('setting.index')->with('error', 'Google 绑定失败，请重试。');
        }
    }

    /**
     * 解绑 Google 账号（个人解绑）
     */
    public function unbindGoogle()
    {
        $user = Auth::user();
        $user->google_id = null;
        // 如有 google_email 字段可一并清空
        // $user->google_email = null;
        $user->save();
        return redirect()->route('setting.index')->with('success', 'Google 账号已解绑！');
    }
} 