<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Custom;
use App\Models\FAQ;
use App\Models\HomePage;
use App\Models\NoticeBoard;
use App\Models\PackageTransaction;
use App\Models\Page;
 use App\Models\Support;
use App\Models\User;
use Carbon\Carbon;


use App\Models\Applicant;
use App\Models\AppliedJob;
use App\Models\Interview;

class HomeController extends Controller
{
    public function index()
    {
        if (\Auth::check()) {

            $result['totalNote'] = NoticeBoard::where('parent_id', parentId())->count();
            $result['totalContact'] = Contact::where('parent_id', parentId())->count();
            $result['totalAppliedJob'] = AppliedJob::where('parent_id', parentId())->count();
            $result['totalApplicant'] = Applicant::where('parent_id', parentId())->count();
            $result['totalNote'] = NoticeBoard::where('parent_id', \Auth::user()->id)->count();
            $result['totalContact'] = Contact::where('parent_id', \Auth::user()->id)->count();
            $result['todayApplicant'] = Applicant::where(\DB::raw("DATE(created_at) = '" . date('Y-m-d') . "'"))->where('parent_id', parentId())->count();
            $result['todayInterviews'] = Interview::where('interview_date', date('Y-m-d'))->where('parent_id', parentId())->count();
            $result['recentJobs'] = AppliedJob::where('parent_id', parentId())->orderBy('id', 'desc')->limit(5)->get();
            $result['recentApplicant'] = Applicant::where('parent_id', parentId())->orderBy('id', 'desc')->limit(5)->get();
            // 获取最近面试记录，确保关联数据正确加载
            // 对于hod用户，需要特殊处理数据权限
            $currentUser = \Auth::user();
            $parentIdValue = parentId();

            // 调试信息：记录当前用户信息
            \Log::info('Dashboard Recent Interviews Debug', [
                'user_id' => $currentUser->id,
                'user_type' => $currentUser->type,
                'user_roles' => $currentUser->getRoleNames(),
                'parent_id_value' => $parentIdValue,
                'user_parent_id' => $currentUser->parent_id
            ]);

            // 方案1：使用JOIN查询确保数据完整性
            $result['recentInterviews'] = Interview::select([
                    'interviews.*',
                    'applicants.name as applicant_name',
                    'applicants.email as applicant_email',
                    'users.name as user_name'
                ])
                ->leftJoin('applicants', 'interviews.applicant', '=', 'applicants.id')
                ->leftJoin('users', 'interviews.assign_user', '=', 'users.id')
                ->where('interviews.parent_id', $parentIdValue)
                ->where(function($query) use ($parentIdValue) {
                    // 确保申请人也在权限范围内，或者允许为空
                    $query->where('applicants.parent_id', $parentIdValue)
                          ->orWhereNull('applicants.id');
                })
                ->orderBy('interviews.id', 'desc')
                ->limit(5)
                ->get();

            // 调试信息：记录获取到的面试数据
            \Log::info('Dashboard Recent Interviews Data', [
                'interviews_count' => $result['recentInterviews']->count(),
                'interviews_data' => $result['recentInterviews']->map(function($interview) {
                    // 检查申请人数据是否存在
                    $applicantExists = \App\Models\Applicant::find($interview->applicant);
                    $applicantInScope = \App\Models\Applicant::where('id', $interview->applicant)
                        ->where('parent_id', parentId())
                        ->first();

                    return [
                        'interview_id' => $interview->id,
                        'interview_parent_id' => $interview->parent_id,
                        'applicant_id' => $interview->applicant,
                        'applicant_exists' => $applicantExists ? true : false,
                        'applicant_in_scope' => $applicantInScope ? true : false,
                        'applicant_parent_id' => $applicantExists ? $applicantExists->parent_id : null,
                        'applicant_name' => $interview->applicants ? $interview->applicants->name : null,
                        'applicant_email' => $interview->applicants ? $interview->applicants->email : null,
                        'assign_user' => $interview->assign_user,
                        'user_name' => $interview->users ? $interview->users->name : null,
                    ];
                })
            ]);
            $result['settings'] = settings();
            return view('dashboard.index', compact('result'));
        } else {
            if (!file_exists(setup())) {
                header('location:install');
                die;
            } else {

                $landingPage = getSettingsValByName('landing_page');
                $job_list = getSettingsValByName('job_list');

                if ($landingPage == 'on' ||  $job_list == 'on') {
                    if ($landingPage == 'on') {

                        $menus = Page::where('enabled', 1)->get();
                        $FAQs = FAQ::where('enabled', 1)->get();
                        $appliedJobs = AppliedJob::where('status', 'active')->orderBy('id', 'desc')->take(6)->get();
                        return view('layouts.landing', compact(  'menus', 'FAQs', 'appliedJobs'));
                    } else {
                        return redirect()->route('job.list');
                    }
                } else {
                    return redirect()->route('login');
                }



            }
        }
    }

    public function organizationByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $organization = [];
        while ($currentdate <= $end) {
            $organization['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $organization['data'][] = User::where('type', 'owner')->whereMonth('created_at', $month)->whereYear('created_at', $year)->count();
            $currentdate = strtotime('+1 month', $currentdate);
        }


        return $organization;
    }

    public function paymentByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $payment['data'][] = PackageTransaction::whereMonth('created_at', $month)->whereYear('created_at', $year)->sum('amount');
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;
    }
}
