<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 创建申请人投票表的迁移
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applicant_votes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('applicant_id'); // 申请人ID
            $table->unsignedBigInteger('user_id'); // 用户ID
            $table->tinyInteger('vote_value')->default(0); // 投票值：0-5分
            $table->text('comment')->nullable(); // 投票评论
            $table->timestamps();
            
            // 添加索引
            $table->index('applicant_id');
            $table->index('user_id');
            $table->index('vote_value');
            
            // 确保每个用户只能对一个申请人投一次票
            $table->unique(['user_id', 'applicant_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicant_votes');
    }
};
