<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppliedJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_title',
        'job_description',
        'job_requirement',
        'job_location',
        'job_category',
        'job_skill',
        'position',
        'start_date',
        'end_date',
        'status',
        'code',
        'question',
        'min_experience',
        'max_experience',
        'min_salary',
        'max_salary',
        'salary_period',
        'job_type',
        'parent_id',
        'is_active',
    ];

    public static $status = [
        'active' => 'Active',
        'in_active' => 'In Active',
    ];

    public static $salary_period = [
        'hourly' => 'Hourly',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
    ];

    public function locations()
    {
        return $this->hasOne('App\Models\Location', 'id', 'job_location');
    }

    public function categories()
    {
        return $this->hasOne('App\Models\Category', 'id', 'job_category');
    }

    public function userCode()
    {
        return $this->hasOne('App\Models\user', 'id', 'parent_id');
    }

    public function types()
    {
        return $this->hasOne('App\Models\Type', 'id', 'job_type');
    }

    public function questions()
    {
        $questionIds = explode(',', $this->question);
        return Question::whereIn('id', $questionIds)->get();

    }

    public function skills()
    {
        $skillIds = explode(',', $this->job_skill);
        return Skill::whereIn('id', $skillIds)->get();

    }

    /**
     * 职位关联的用户
     * 通过多对多关系关联有权限处理该职位的用户
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'applied_job_user')
                    ->withPivot('source_type', 'source_id')
                    ->withTimestamps();
    }

    /**
     * 检查指定用户是否有权限处理该职位
     */
    public function isAssignedTo($userId)
    {
        return $this->users()->where('user_id', $userId)->exists();
    }

    /**
     * 为职位分配用户组中的所有用户和特定用户
     */
    public function assignToUsers($userIds = [], $userGroupIds = [])
    {
        // 获取所有用户组中的用户
        $groupUserIds = [];
        if (!empty($userGroupIds)) {
            $groupUsers = \App\Models\UserGroupMember::whereIn('group_id', $userGroupIds)
                                                    ->pluck('user_id')
                                                    ->toArray();
            $groupUserIds = array_map(function($userId) use ($userGroupIds) {
                // 对于每个用户，找到他所属的第一个选定用户组
                $groupId = \App\Models\UserGroupMember::where('user_id', $userId)
                                                   ->whereIn('group_id', $userGroupIds)
                                                   ->first()->group_id ?? null;
                return [
                    'user_id' => $userId,
                    'source_type' => 'group',
                    'source_id' => $groupId
                ];
            }, $groupUsers);
        }
        
        // 准备直接选择的用户
        $directUserIds = [];
        if (!empty($userIds)) {
            $directUserIds = array_map(function($userId) {
                return [
                    'user_id' => $userId, 
                    'source_type' => 'direct',
                    'source_id' => null
                ];
            }, $userIds);
        }
        
        // 合并所有用户，确保唯一性
        $allUsers = [];
        foreach (array_merge($groupUserIds, $directUserIds) as $user) {
            $allUsers[$user['user_id']] = [
                'source_type' => $user['source_type'],
                'source_id' => $user['source_id']
            ];
        }
        
        // 删除现有关联并重新添加
        $this->users()->detach();
        foreach ($allUsers as $userId => $pivot) {
            $this->users()->attach($userId, $pivot);
        }
        
        return $this;
    }
}
