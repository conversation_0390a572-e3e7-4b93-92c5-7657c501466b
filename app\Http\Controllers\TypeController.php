<?php

namespace App\Http\Controllers;

use App\Models\Type;
use Illuminate\Http\Request;

class TypeController extends Controller
{

    public function index()
    {
        if(\Auth::user()->can('manage type'))
        {
            $types = Type::where('parent_id', parentId())->get();
            return view('type.index', compact('types'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        return view('type.create');
    }


    public function store(Request $request)
    {
        if(\Auth::user()->can('create type'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'type' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $type            = new Type();
            $type->type      = $request->type;
            $type->parent_id = parentId();
            $type->save();
            return redirect()->back()->with('success', __('Type successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function show(Type $type)
    {
        //
    }


    public function edit(Type $type)
    {
        return view('type.edit', compact('type'));
    }


    public function update(Request $request, Type $type)
    {
        if(\Auth::user()->can('edit type'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'type' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $type->type = $request->type;
            $type->save();
            return redirect()->back()->with('success', __('Type successfully updated.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function destroy(Type $type)
    {
        if(\Auth::user()->can('delete type'))
        {
            $type->delete();
            return redirect()->back()->with('success', 'Type successfully deleted.');
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }
}
