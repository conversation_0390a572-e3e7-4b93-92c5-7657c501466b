@extends('layouts.app')
@section('page-title')
    {{ __('Dashboard') }}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item" aria-current="page">{{ __('Dashboard') }}</li>
@endsection
@push('script-page')
@endpush
@section('content')
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar bg-light-secondary">
                                <i class="ti ti-users f-24"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1">{{ __('Total Applied Jobs') }}</p>
                            <div class="d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">{{ $result['totalAppliedJob'] }}</h4>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar bg-light-warning">
                                <i class="ti ti-package f-24"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1">{{ __('Total Applicants') }}</p>
                            <div class="d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">{{ $result['totalApplicant'] }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar bg-light-primary">
                                <i class="ti ti-history f-24"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1">{{ __('Total Interviews') }}</p>
                            <div class="d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">{{ $result['todayInterviews'] }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="avtar bg-light-danger">
                                <i class="ti ti-credit-card f-24"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-1">{{ __('Total Contact') }}</p>
                            <div class="d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">{{ $result['totalContact'] }}
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">

                        <div class="row align-items-center g-2">
                            <div class="col">
                                <h5>{{ __('Recent Interviews') }}</h5>
                            </div>

                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div class="dt-responsive table-responsive">
                            <table class="table table-hover advance-datatable">
                                <thead>
                                    <tr>
                                        <th>{{ __('Name') }}</th>
                                        <th>{{ __('Email') }}</th>
                                        <th>{{ __('Interview Date') }}</th>
                                        <th>{{ __('Interview Time') }}</th>
                                        <th>{{ __('Assign User') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($result['recentInterviews'] as $interview)
                                        <tr role="row">
                                            <td class="sorting_1">
                                                @if(!empty($interview->applicant_name))
                                                    {{ $interview->applicant_name }}
                                                @else
                                                    <span class="text-muted">-</span>
                                                    {{-- 调试信息 --}}
                                                    <small class="text-danger">(ID: {{ $interview->applicant }})</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if(!empty($interview->applicant_email))
                                                    {{ $interview->applicant_email }}
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                {{ dateFormat($interview->interview_date) }}
                                            </td>
                                            <td>
                                                {{ timeFormat($interview->interview_start_time) }}
                                            </td>
                                            <td>
                                                {{ !empty($interview->user_name) ? $interview->user_name : '-' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">

                        <div class="row align-items-center g-2">
                            <div class="col">
                                <h5>{{ __('Recent Applied Jobs') }}</h5>
                            </div>

                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div class="dt-responsive table-responsive">
                            <table class="table table-hover advance-datatable">
                                <thead>
                                    <tr>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Title') }}</th>
                                        <th>{{ __('Category') }}</th>
                                        <th>{{ __('Type') }}</th>
                                        <th>{{ __('Start Date') }}</th>
                                        <th>{{ __('End Date') }}</th>
                                        <th>{{ __('Location') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($result['recentJobs'] as $appliedJob)
                                        <tr>
                                            <td>
                                                @if ($appliedJob->status == 'active')
                                                    <span class="badge text-bg-success">
                                                        {{ ucfirst($appliedJob->status) }}</span>
                                                @else
                                                    <span class="badge text-bg-danger">
                                                        {{ ucfirst($appliedJob->status) }}</span>
                                                @endif
                                            </td>
                                            <td>{{ $appliedJob->job_title }} </td>
                                            <td> {{ !empty($appliedJob->categories) ? $appliedJob->categories->category : '-' }}
                                            </td>
                                            <td> {{ !empty($appliedJob->types) ? $appliedJob->types->type : '-' }} </td>
                                            <td>{{ dateFormat($appliedJob->start_date) }} </td>
                                            <td>{{ dateFormat($appliedJob->end_date) }} </td>
                                            <td>{{ !empty($appliedJob->locations) ? $appliedJob->locations->location . ',' . $appliedJob->locations->state . ',' . $appliedJob->locations->country : '-' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                         <div class="row align-items-center g-2">
                            <div class="col">
                                <h5>{{ __('Recent Applicants') }}</h5>
                            </div>

                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div class="dt-responsive table-responsive">
                            <table class="table table-hover advance-datatable">
                                <thead>
                                    <tr>
                                        <th>{{ __('Name') }}</th>
                                        <th>{{ __('Applied For') }}</th>
                                        <th>{{ __('Applied Date') }}</th>
                                        <th>{{ __('Experience') }}</th>
                                        <th>{{ __('Expected Salary') }}</th>
                                        <th>{{ __('Stage') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($result['recentApplicant'] as $applicant)
                                        <tr role="row">
                                            <td class="sorting_1">
                                                <a href="{{ route('applicant.show', \Crypt::encrypt($applicant->id)) }}"
                                                    class="text-body">{{ $applicant->name }}</a>
                                                <br>
                                                {{ $applicant->email }}
                                            </td>
                                            <td>
                                                {{ !empty($applicant->appliedJob) ? $applicant->appliedJob->job_title : '' }}
                                            </td>
                                            <td>
                                                {{ dateFormat($applicant->created_at) }}
                                            </td>
                                            <td>
                                                {{ ucfirst($applicant->experience) }}
                                            </td>

                                            <td>
                                                {{ priceFormat($applicant->expected_salary) }}
                                            </td>
                                            <td>
                                                {{ !empty($applicant->stages) ? $applicant->stages->stage : '' }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
@endsection
