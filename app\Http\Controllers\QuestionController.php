<?php

namespace App\Http\Controllers;

use App\Models\Question;
use Illuminate\Http\Request;

class QuestionController extends Controller
{

    public function index()
    {
        if (\Auth::user()->can('manage question')) {
            $questions = Question::where('parent_id', parentId())->get();
            return view('question.index', compact('questions'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        return view('question.create');
    }


    public function store(Request $request)
    {
        if (\Auth::user()->can('create question')) {
            $validator = \Validator::make(
                $request->all(), [
                    'question' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $question = new Question();
            $question->question = $request->question;
            $question->required_status = $request->required_status;
            $question->parent_id = parentId();
            $question->save();

            return redirect()->back()->with('success', __('Question successfully created.'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function show(Question $question)
    {
        //
    }


    public function edit(Question $question)
    {
        return view('question.edit', compact('question'));
    }


    public function update(Request $request, Question $question)
    {

        if(\Auth::user()->can('edit question'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'question' => 'required',

                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $question->question    = $request->question;
            $question->required_status = $request->required_status;
            $question->save();
            return redirect()->back()->with('success', __('Question successfully updated!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function destroy(Question $question)
    {
        if(\Auth::user()->can('delete question'))
        {
            $question->delete();
            return redirect()->back()->with('success', 'Question successfully deleted.');
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }
}
