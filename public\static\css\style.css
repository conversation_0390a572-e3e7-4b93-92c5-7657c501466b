html {
	font-size: 16px;
}

body {
	font-family: Arial, sans-serif;
	background-color: #F2F2F2;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
	margin: 0;
	padding: 0;
}
a{
	text-decoration: none;
}

.custom-btn-color1 {
	background-color: #09A7C9;
	/* 自定义背景颜色 */
	border-color: transparent;
	/* 自定义边框颜色 */
	color: #FFFFFF;
	/* 文字颜色 */
}
.custom-btn-color2{
	background-color: #979797;
	color: #FFFFFF;
}

.step-side {
	width: 100%;
	height: 100vh;
	background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
	background-size: 100% 100%;
}

.header-logo {
	width: 24.5rem;
	height: auto;
	overflow: hidden;
	padding-top: 3.625rem;
	margin-left: 3.625rem;
	margin-bottom: 12.5rem;
}

.header-logo>img {
	display: block;
	width: 100%;
}

.step-wrap {
	margin-left: 9.6875rem;
}

.step {
	position: relative;
	margin-bottom: 5rem;
}

.step::after {
	content: "";
	display: block;
	width: 0px;
	height: 3.4375rem;
	border: 0.0625rem solid #FFFFFF;
	opacity: 0.35;
	position: absolute;
	bottom: -4.2rem;
	left: 1.875rem;
	z-index: 9;
}

.step:last-of-type::after {
	display: none;
}

.step-left-round {
	width: 3.75rem;
	height: 3.75rem;
	margin-right: 2.625rem;
}

.step-left-round>div {
	width: 2.625rem;
	height: 2.625rem;
	border-radius: 50%;
	background: #693F8D 0% 0% no-repeat padding-box;
	border: 0.125rem solid #8F7A9F;

}

.step-number {
	font: normal normal normal 1.125rem/1.3125rem Raleway;
	letter-spacing: 0rem;
	color: #FFFFFF;
	opacity: 0.7;
	margin-bottom: 0.9375rem;
	font-weight: 200;
}

.step-title {
	font: normal normal normal 1.5rem/1.8125rem Raleway;
	color: #FFFFFF;
	opacity: 0.7;
	font-weight: 200;
}

.current-step>.step-left-round>div {
	background: url("../images/011F917FF1F774BB00E90E38D619254A.png") no-repeat;
	border: 0.5625rem solid rgba(255, 255, 255, 0.15);
	box-sizing: content-box;
}

.current-step .step-number {
	font: normal normal normal 18px/21px Raleway;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
}

.current-step .step-title {
	font: normal normal 600 24px/29px Raleway;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
}


.form-container {
	background: #FFFFFF 0% 0% no-repeat padding-box;
	padding: 1.875rem 6.25rem;
	margin: 1.25rem 0;
	border-radius: 1.25rem;
	box-shadow: 0px 3px 10px #00000005;
}

.search-btn {
	width: 7.5rem;
	height: 2.625rem;
	background: #09A7C9 0% 0% no-repeat padding-box;
	border: 0.0625rem solid #E5E5E5;
	border-radius: 0.3125rem;
	opacity: 1;
	text-align: center;
	font: normal normal medium 1rem/1.1875rem Raleway;
	letter-spacing: 0;
	color: #FFFFFF;
	opacity: 1;
}

.custom-input {
	height: 2.625rem;
	width: 100%;
	background-color: #FFFFFF;
	border: 0.0625rem solid #E5E5E5;
	border-radius: 0.3125rem;
	opacity: 1;
}

.application-form h4 {
	text-align: left;
	font: normal normal 600 1rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #09A7C9;
	opacity: 1;
	line-height: 2.5rem;
}

.application-form p {
	line-height: 2.5rem;
}

.application-form p>span:first-of-type {
	letter-spacing: 0px;
	color: #060606;
	opacity: 1;
	font-size: 0.875rem;
}

.application-form p>span:last-of-type {
	font: normal normal medium 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #FF5A5A;
	opacity: 1;
}

.notice-box {}

.notice-text>div {
	margin-right: 0.625rem;
	height: 1.5rem;
	display: flex;
	justify-content: center;
	align-items: center;
}

.notice-text>p {
	text-align: left;
	font: normal normal normal 0.875rem/1.5rem Raleway;
	color: #060606;
	flex-grow: 1;
	flex-basis: 0;
}

.next-btn {
	width: 17.75rem;
	height: 3.75rem;
	/* background: #09A7C9 0% 0% no-repeat padding-box; */
	border-radius: 2.3125rem;
	opacity: 1;
	font: normal normal 600 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #FFFFFF;
}



/* preview submit页面样式 */
.right-card-wrap {
	background: #FFFFFF 0% 0% no-repeat padding-box;
	box-shadow: 0rem 0.1875rem 0.625rem #00000005;
	border-radius: 1.25rem;
	width: 100%;
	height: auto;
	overflow: hidden;
	margin-bottom: 1.25rem;
}
.content-title{
	width: 100%;
	height: 3.75rem;
	background: #693F8D 0% 0% no-repeat padding-box;
	box-shadow: 0rem 0.1875rem 0.625rem #00000005;
	padding: 0 8%;
}
.content-title>p{
	text-align: left;
	font: normal normal bold 1.5rem/1.8125rem Raleway;
	letter-spacing: 0;
	color: #FFFFFF;
	opacity: 1;
}
.right-card-content{
	padding: 1.875rem 8%;
}
.right-card-content>p{
	text-align: left;
	font: normal normal normal 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #060606;
	opacity: 1;
	margin: 1rem 0;
}
.right-card-content>h5{
	text-align: left;
	font: normal normal 600 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #693F8D;
	opacity: 1;
	margin-top: 3rem;
}
.name-input{
	/* background-color: #F2F2F2; */
	background-color: #FBFBFB;
}
.name-input::placeholder {
  color: #BEBEBE;
}
.current-date{
	text-align: left;
	font: normal normal normal 0.875rem/2.625rem Raleway;
	letter-spacing: 0;
	color: #060606;
	opacity: 1;
}

/* preview页面样式 */
.preview-item>label{
	text-align: left;
	font: normal normal normal 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #060606;
	opacity: 1;
}
.preview-item>p{
	text-align: left;
	font: normal normal 600 1.25rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #693F8D;
	opacity: 1;
	margin-top: 0.375rem;
	margin-bottom: 2rem;
}
.custom-h4{
	text-align: left;
	font: normal normal bold 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #693F8D;
	opacity: 1;
	margin: 1.25rem 0;
}
.table-th{
	text-align: left;
	font: normal normal normal 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #060606;
	opacity: 1;
}
.table-td{
	text-align: left;
	font: normal normal 600 1.25rem/2rem Raleway;
	letter-spacing: 0;
	color: #693F8D;
	opacity: 1;
}
.custom-line{
	width: 100%;
	height: 1px;
	background-color: #F6F6F6;
	margin: 1.25rem 0 2.5rem;
}


/*  */
.personal-item{
	margin-bottom: 1.25rem;
}
.p-style{
	text-align: left;
	font: normal normal 600 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #1C1C1C;
	opacity: 1;
	margin-bottom: 0.625rem;
}
.information::after{
	height: 9rem;
	bottom: -4.3rem;
}
.information-list{
	margin: 0;
	padding: 0;
	padding-left: 1.2rem;
	margin-top: 0.625rem;
}
.information-list>li{
	text-align: left;
	font: normal normal normal 1.125rem/1.625rem Raleway;
	letter-spacing: 0;
	color: #FFFFFF;
	opacity: 1;
}
.add-row-btn{
	width: 10.875rem;
	height: 2.8125rem;
	/* UI Properties */
	background: #E6FBFF 0% 0% no-repeat padding-box;
	border: 0.0625rem solid #0087A4;
	border-radius: 0.625rem;
	opacity: 1;
}
.add-row-btn>i{
	color: #0087A4;
	font-size: 1.1875rem;
	margin-right: 0.625rem;
}
.add-row-btn>span{
	font: normal normal medium 0.875rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #0087A4;
	opacity: 1;
}
.min-width-150{
	min-width: 9.375rem;
}
.min-width-200{
	min-width: 12.5rem;
}
.upload-file{
	width: 19.6875rem;
	height: 10.6875rem;
	position: relative;
}
.choose-file{
	width: 100%;
	height: 100%;
	background: #E6FBFF 0% 0% no-repeat padding-box;
	border: 0.0625rem dashed #0087A4;
	border-radius: 0.625rem;
	opacity: 1;
	
}
#my-awesome-dropzone{
	position: absolute;
	width: 100%;
	height: 100%;
	opacity: 0;
	z-index: 9;
	top: 0;
	left: 0;
}
.choose-file>h6{
	font: normal normal medium 1rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #0087A4;
	opacity: 1;
	margin: 0.625rem 0;
}
.choose-file>p{
	font: normal normal 300 0.75rem/1.125rem Raleway;
	letter-spacing: 0;
	color: #0087A4;
	opacity: 1;
}
.opacity-input{
	width: 100%;
	height: 100%;
	border: none;
	background-color: transparent;
}
.step-fixed{
	height: 4.1875rem;
	background: #FFFFFF 0% 0% no-repeat padding-box;
	box-shadow: 0rem 0.1875rem 0.375rem #00000029;
	border: 0.0625rem solid #D29CFF;
	border-radius: 6.25rem;
	opacity: 1;
}
.step-fixed-item{
	height: 4.1875rem;
	cursor: pointer;
}
.step-fixed-item span:first-of-type{
	width: 2.125rem;
	height: 2.125rem;
	background: #EBEBEB 0% 0% no-repeat padding-box;
	border-radius: 50%;
	text-align: center;
	line-height: 2.125rem;
	font: normal normal medium 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #A1A1A1;
}
.step-fixed-item span:last-of-type{
	text-align: left;
	font: normal normal medium 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #262626;
	opacity: 1;
}
.current-step-item span:first-of-type{
	background-color: #693F8D;
	color: #FFFFFF;
}
.current-step-item span:last-of-type{
	font-weight: bold;
}


/*  */
.custom-next-btn{
	width: 12.6875rem;
	height: 4.625rem;
	/* UI Properties */
	background: transparent linear-gradient(115deg, #09A7C9 0%, #1DBEE1 100%) 0% 0% no-repeat padding-box;
	border-radius: 2.625rem;
	opacity: 1;
	box-sizing: border-box;
	padding: 0 1.5rem;
}
.custom-next-btn>span{
	font: normal normal 600 1.125rem/1.3125rem Raleway;
	letter-spacing: 0;
	color: #FFFFFF;
	opacity: 1;
}
.custom-next-btn>i{
	font-size: 1.8125rem;
	color: #FFFFFF;
}


.step-appForm-item .step-appForm-item-number{
	width: 2.5rem;
	height: 2.5rem;
	text-align: center;
	line-height: 2.5rem;
	font-size: 1.25rem;
	color: #D5D5D5;
	border-radius: 50%;
	border: 0.0625rem solid #D5D5D5;
}
.step-appForm-item .step-appForm-item-content h4{
	font-size: 1rem;
	line-height: 1.25rem;
	color: #919191;
}
.step-appForm-item .step-appForm-item-content p{
	font-size: 0.75rem;
	line-height: 1rem;
	color: #919191;
}
.selected-appForm-item .step-appForm-item-number{
	background: #693F8D 0% 0% no-repeat padding-box;
	color: #ffffff;
	border-color: #693F8D;
}
.selected-appForm-item .step-appForm-item-content h4{
	color: #1C1C1C;
	font-weight: bold;
}
.appForm-header{
	height: 7.5rem;
	background-color: #ffffff;
	box-shadow: 0 0.1875rem 0.625rem #00000005;
}

.title-h2{
	font-size: 1.875rem;
	line-height: 2.1875rem;
	color: #ffffff;
	font-weight: bold;
}

.banner-wrap{
	background-position: center;
	background-size: cover;
	aspect-ratio: 6.65/1;
	height: auto;
}

.job-reference-number{
	background: #FFFFFF 0% 0% no-repeat padding-box;
	box-shadow: 0 0.1875rem 0.75rem #00000029;
	border-radius: 0.9375rem;
}
.title-h5{
	font-weight: bold;
	font-size: 1rem;
	line-height: 1.1875rem;
	color: #09A7C9;
}
.job-reference-number-p{
	font-size: 0.875rem;
	line-height: 1rem;
	
}
.job-reference-number-p>span:first-of-type{
	color: #919191;
}
.job-reference-number-p>span:nth-of-type(2){
	color: #FE2D2D;
}
.appForm-content{
	transform: translateY(-3.125rem);
}

.text-p{
	font-size: 0.875rem;
	line-height: 1.125rem;
	color: #060606;
}
.title-h3{
	font-weight: bold;
	font-size: 1.25rem;
	line-height: 1.5rem;
	color: #693F8D;
}
.personal-section{
	background-color: #ffffff;
	border-radius: 0.9375rem;
}

.personal-section-item>label{
	font-size: 0.875rem;
	line-height: 1.125rem;
	font-weight: bold;
	color: #060606;
}
.personal-section-item>input{
	background-color: transparent;
	border-top: 0;
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	border-bottom: 0.0625rem solid #D2D2D2;
	padding-left: 0;
	padding-right: 0;
}
.personal-section-item-btn>div{
	width: 49%;
	height: 3.125rem;
	
}
.personal-section-item-btn>div>label{
	width: 100%;
	height: 100%;
	background-color: #FBFBFB;
	border: 1px solid #EBEBEB;
	color: #B1B1B1;
}
.btn-custom-outline-success:hover{
	background-color: #F0FFEC !important;
	border: 0.0625rem solid #97D389 !important;
	color: #6BBF57 !important;
}
.btn-check:active+.btn-custom-outline-success, .btn-check:checked+.btn-custom-outline-success, .btn-custom-outline-success.active, .btn-custom-outline-success.dropdown-toggle.show, .btn-custom-outline-success:active{
	background-color: #F0FFEC !important;
	border: 0.0625rem solid #97D389 !important;
	color: #6BBF57 !important;
}
.btn-custom-outline-danger:hover{
	background-color: #feeceb !important;
	border: 0.0625rem solid #DC3545 !important;
	color: #DC3545 !important;
}
.btn-check:active+.btn-custom-outline-danger, .btn-check:checked+.btn-custom-outline-danger, .btn-custom-outline-danger.active, .btn-custom-outline-danger.dropdown-toggle.show, .btn-custom-outline-danger:active{
	background-color: #feeceb !important;
	border: 0.0625rem solid #DC3545 !important;
	color: #DC3545 !important;
}

.fixed-step{
	width: 78.0625rem;
	height: 4.75rem;
	background: #FFFFFF 0% 0% no-repeat padding-box;
	box-shadow: 0 0.1875rem 0.375rem #00000029;
	border-radius: 1.5625rem 1.5625rem 0 0;
	left: 50%;
	margin-left: -39.03125rem;
}

/* xs 超小屏 */
@media (min-width: 0) {
	/* html {
		font-size: 5px;
	} */

	.step-side{
		width: 100%;
		height: 70vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}

	.header-logo {
		width: 20rem;
		height: auto;
		overflow: hidden;
		padding-top: 2rem;
		margin: 0 auto 0;
	}

	.step-wrap {
		transform: scale(0.8);
		margin-left: 0;
	}
	.form-container {
		padding: 1.5rem;
	}
	.search-btn {
		width: 5rem;
	}
}

/* sm 小屏 */
@media (min-width: 576px) {
	.step-side{
		width: 100%;
		height: 80vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}
	.header-logo {
		width: 24.5rem;
		height: auto;
		overflow: hidden;
		padding-top: 3.625rem;
		margin-left: 3.625rem;
		margin-bottom: 5rem;
	}

	.step-wrap {
		transform: scale(1);
		margin-left: 5rem;
	}
	.form-container {
		padding: 2rem;
	}
	.search-btn {
		width: 7.5rem;
	}
}

/* md 中屏 */
@media (min-width: 768px) {
	.step-side{
		width: 100%;
		height: 100vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}
	.header-logo {
		width: 24.5rem;
		height: auto;
		overflow: hidden;
		padding-top: 3.625rem;
		margin-left: 3.625rem;
		margin-bottom: 12.5rem;
	}

	.step-wrap {
		transform: scale(1);
		margin-left: 9.6875rem;
	}
	.form-container {
		padding: 2rem;
	}
	.search-btn {
		width: 7.5rem;
	}
}

/* lg 大屏 */
@media (min-width: 992px) {
	.step-side{
		width: 100%;
		height: 100vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}
	.header-logo {
		width: 24.5rem;
		height: auto;
		overflow: hidden;
		padding-top: 3.625rem;
		margin-left: 1rem;
		margin-bottom: 12.5rem;
	}

	.step-wrap {
		transform: scale(0.9);
		margin-left: 1rem;
	}
	.form-container {
		padding: 2rem;
	}
	.search-btn {
		width: 7.5rem;
	}
}

/* xl 超大屏 */
@media (min-width: 1200px) {
	.step-side{
		width: 100%;
		height: 100vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}
	.header-logo {
		width: 24.5rem;
		height: auto;
		overflow: hidden;
		padding-top: 3.625rem;
		margin-left: 0rem;
		margin-bottom: 12.5rem;
	}

	.step-wrap {
		transform: scale(1);
		margin-left: 2rem;
	}
	.form-container {
		padding: 1.875rem 4rem;
	}
	.search-btn {
		width: 7.5rem;
	}
}

/* xxl 特大屏 */
@media (min-width: 1400px) {
	.container{
		width: 1102px;
	}
	.step-side{
		width: 100%;
		height: 100vh;
		background: url("../images/F2FBD5B027675EBF34C1C079ECA18B55.png") no-repeat;
		background-size: 100% 100%;
	}
	.header-logo {
		width: 24.5rem;
		height: auto;
		overflow: hidden;
		padding-top: 3.625rem;
		margin-left: 3.625rem;
		margin-bottom: 12.5rem;
	}

	.step-wrap {
		transform: scale(1);
		margin-left: 9.6875rem;
	}
	.form-container {
		padding: 1.875rem 6.25rem;
	}
	.search-btn {
		width: 7.5rem;
	}
}