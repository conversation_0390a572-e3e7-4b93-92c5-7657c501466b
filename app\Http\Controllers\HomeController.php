<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\FAQ;
use App\Models\NoticeBoard;
use App\Models\Page;
use App\Models\User;


use App\Models\Applicant;
use App\Models\AppliedJob;
use App\Models\Interview;

class HomeController extends Controller
{
    public function index()
    {
        if (\Auth::check()) {

            $result['totalNote'] = NoticeBoard::where('parent_id', parentId())->count();
            $result['totalContact'] = Contact::where('parent_id', parentId())->count();
            $result['totalAppliedJob'] = AppliedJob::where('parent_id', parentId())->count();
            $result['totalApplicant'] = Applicant::where('parent_id', parentId())->count();
            $result['totalNote'] = NoticeBoard::where('parent_id', \Auth::user()->id)->count();
            $result['totalContact'] = Contact::where('parent_id', \Auth::user()->id)->count();
            $result['todayApplicant'] = Applicant::where(\DB::raw("DATE(created_at) = '" . date('Y-m-d') . "'"))->where('parent_id', parentId())->count();
            $result['todayInterviews'] = Interview::where('interview_date', date('Y-m-d'))->where('parent_id', parentId())->count();
            $result['recentJobs'] = AppliedJob::where('parent_id', parentId())->orderBy('id', 'desc')->limit(5)->get();
            $result['recentApplicant'] = Applicant::where('parent_id', parentId())->orderBy('id', 'desc')->limit(5)->get();
            // 获取最近面试记录，根据用户角色进行权限控制
            $currentUser = \Auth::user();

            if ($currentUser->hasRole('hod')) {
                // hod用户只能查看自己有权限的工作的申请人面试记录
                $result['recentInterviews'] = Interview::select([
                        'interviews.*',
                        'applicants.name as applicant_name',
                        'applicants.email as applicant_email',
                        'users.name as user_name'
                    ])
                    ->leftJoin('applicants', 'interviews.applicant', '=', 'applicants.id')
                    ->leftJoin('users', 'interviews.assign_user', '=', 'users.id')
                    ->leftJoin('applied_job_user', function($join) use ($currentUser) {
                        $join->on('applicants.job', '=', 'applied_job_user.applied_job_id')
                             ->where('applied_job_user.user_id', '=', $currentUser->id);
                    })
                    ->where('interviews.parent_id', parentId())
                    ->where(function($query) {
                        // 确保申请人也在权限范围内，或者允许为空
                        $query->where('applicants.parent_id', parentId())
                              ->orWhereNull('applicants.id');
                    })
                    ->whereNotNull('applied_job_user.id') // 确保hod用户对该工作有权限
                    ->orderBy('interviews.id', 'desc')
                    ->limit(5)
                    ->get();
            } else {
                // 其他角色（owner, manager）可以查看所有面试记录
                $result['recentInterviews'] = Interview::select([
                        'interviews.*',
                        'applicants.name as applicant_name',
                        'applicants.email as applicant_email',
                        'users.name as user_name'
                    ])
                    ->leftJoin('applicants', 'interviews.applicant', '=', 'applicants.id')
                    ->leftJoin('users', 'interviews.assign_user', '=', 'users.id')
                    ->where('interviews.parent_id', parentId())
                    ->where(function($query) {
                        // 确保申请人也在权限范围内，或者允许为空
                        $query->where('applicants.parent_id', parentId())
                              ->orWhereNull('applicants.id');
                    })
                    ->orderBy('interviews.id', 'desc')
                    ->limit(5)
                    ->get();
            }
            $result['settings'] = settings();
            return view('dashboard.index', compact('result'));
        } else {
            if (!file_exists(setup())) {
                header('location:install');
                die;
            } else {

                $landingPage = getSettingsValByName('landing_page');
                $job_list = getSettingsValByName('job_list');

                if ($landingPage == 'on' ||  $job_list == 'on') {
                    if ($landingPage == 'on') {

                        $menus = Page::where('enabled', 1)->get();
                        $FAQs = FAQ::where('enabled', 1)->get();
                        $appliedJobs = AppliedJob::where('status', 'active')->orderBy('id', 'desc')->take(6)->get();
                        return view('layouts.landing', compact(  'menus', 'FAQs', 'appliedJobs'));
                    } else {
                        return redirect()->route('job.list');
                    }
                } else {
                    return redirect()->route('login');
                }



            }
        }
    }

    public function organizationByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $organization = [];
        while ($currentdate <= $end) {
            $organization['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            $organization['data'][] = User::where('type', 'owner')->whereMonth('created_at', $month)->whereYear('created_at', $year)->count();
            $currentdate = strtotime('+1 month', $currentdate);
        }


        return $organization;
    }

    public function paymentByMonth()
    {
        $start = strtotime(date('Y-01'));
        $end = strtotime(date('Y-12'));

        $currentdate = $start;

        $payment = [];
        while ($currentdate <= $end) {
            $payment['label'][] = date('M-Y', $currentdate);

            $month = date('m', $currentdate);
            $year = date('Y', $currentdate);
            // PackageTransaction模型不存在，暂时注释
            // $payment['data'][] = PackageTransaction::whereMonth('created_at', $month)->whereYear('created_at', $year)->sum('amount');
            $payment['data'][] = 0;
            $currentdate = strtotime('+1 month', $currentdate);
        }

        return $payment;
    }
}
