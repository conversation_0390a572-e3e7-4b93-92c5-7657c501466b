{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "anhskohbo/no-captcha": "^3.5", "bacon/bacon-qr-code": "^3.0", "guzzlehttp/guzzle": "^7.2", "kkomelin/laravel-translatable-string-exporter": "^1.21", "lab404/laravel-impersonate": "^1.7", "laravel/framework": "^9.11", "laravel/sanctum": "^2.14.1", "laravel/socialite": "^5.21", "laravel/tinker": "^2.7", "laravel/ui": "^4.2", "laravelcollective/html": "^6.1", "mashape/unirest-php": "^3.0", "pragmarx/google2fa-laravel": "^2.2", "spatie/laravel-permission": "^5.10", "srmklive/paypal": "~3.0", "stripe/stripe-php": "^7.36", "whichbrowser/parser": "^2.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.9", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helper/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}