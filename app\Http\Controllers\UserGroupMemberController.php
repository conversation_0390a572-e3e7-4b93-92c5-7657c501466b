<?php

namespace App\Http\Controllers;

use App\Services\UserGroupService;
use Illuminate\Http\Request;

class UserGroupMemberController extends Controller
{
    protected $service;

    /**
     * 构造函数，注入服务
     */
    public function __construct(UserGroupService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取用户组成员列表
     */
    public function index($groupId)
    {
        $this->authorize('manage-group-member');
        $members = $this->service->getGroupMembers($groupId);
        return response()->json($members);
    }

    /**
     * 添加成员到用户组
     */
    public function store(Request $request, $groupId)
    {
        $this->authorize('manage-group-member');
        $data = $request->validate([
            'user_id' => 'required|integer|exists:users,id',
        ]);
        $member = $this->service->addMember($groupId, $data['user_id']);
        
        // 更新与该用户组关联的所有职位权限
        $this->updateJobPermissions($groupId, $data['user_id']);
        
        return response()->json($member, 201);
    }

    /**
     * 从用户组移除成员（软删除）
     */
    public function destroy($groupId, $userId)
    {
        $this->authorize('manage-group-member');
        $result = $this->service->removeMember($groupId, $userId);
        
        // 更新与该用户组关联的所有职位权限
        $this->updateJobPermissions($groupId, $userId, true);
        
        if ($result) {
            return response()->json(['message' => '移除成功']);
        }
        return response()->json(['message' => '成员不存在'], 404);
    }

    /**
     * 当用户组成员变化时更新职位权限
     * 
     * @param int $groupId 用户组ID
     * @param int $userId 用户ID
     * @param bool $isRemove 是否是移除操作
     */
    private function updateJobPermissions($groupId, $userId, $isRemove = false)
    {
        // 找出所有与该用户组关联的职位
        $affectedJobs = \App\Models\AppliedJobUser::where('source_type', 'group')
                                                 ->where('source_id', $groupId)
                                                 ->pluck('applied_job_id')
                                                 ->unique()
                                                 ->toArray();
        
        if (empty($affectedJobs)) {
            return; // 没有受影响的职位
        }
        
        if ($isRemove) {
            // 移除操作：删除该用户与这些职位的关联（仅当该关联来源是当前用户组时）
            \App\Models\AppliedJobUser::where('user_id', $userId)
                                     ->where('source_type', 'group')
                                     ->where('source_id', $groupId)
                                     ->whereIn('applied_job_id', $affectedJobs)
                                     ->delete();
        } else {
            // 添加操作：为每个职位添加新用户的权限
            foreach ($affectedJobs as $jobId) {
                // 检查该用户是否已经有这个职位的权限（可能是通过其他用户组或直接分配）
                $exists = \App\Models\AppliedJobUser::where('applied_job_id', $jobId)
                                                  ->where('user_id', $userId)
                                                  ->exists();
                
                if (!$exists) {
                    \App\Models\AppliedJobUser::create([
                        'applied_job_id' => $jobId,
                        'user_id' => $userId,
                        'source_type' => 'group',
                        'source_id' => $groupId
                    ]);
                }
            }
        }
    }
} 