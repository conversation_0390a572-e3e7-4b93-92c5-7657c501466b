<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applicants', function (Blueprint $table) {
            $table->id();
            $table->integer('job')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->date('dob')->nullable();
            $table->string('gender')->nullable();
            $table->text('address')->nullable();
            $table->string('profile')->nullable();
            $table->string('resume')->nullable();
            $table->string('experience')->nullable();
            $table->string('expected_salary')->nullable();
            $table->text('cover_letter')->nullable();
            $table->integer('stage')->default(1);
            $table->integer('order')->default(0);
            $table->text('skill')->nullable();
            $table->float('rating')->default(0);
            $table->integer('is_archive')->default(0);
            $table->integer('is_hire')->default(0);
            $table->date('joining_date')->nullable();
            $table->date('ending_date')->nullable();
            $table->text('question')->nullable();
            $table->text('ext_data')->nullable();
            $table->integer('parent_id')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicants');
    }
};
