<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppliedJobUser extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'applied_job_user';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'applied_job_id',
        'user_id',
        'source_type',
        'source_id'
    ];

    /**
     * 获取关联的职位
     */
    public function job()
    {
        return $this->belongsTo(AppliedJob::class, 'applied_job_id');
    }

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取来源用户组（如果存在）
     */
    public function sourceGroup()
    {
        if ($this->source_type === 'group' && $this->source_id) {
            return $this->belongsTo(UserGroup::class, 'source_id');
        }
        return null;
    }
} 