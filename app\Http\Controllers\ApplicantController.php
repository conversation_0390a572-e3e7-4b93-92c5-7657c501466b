<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\ApplicantComment;
use App\Models\AppliedJob;
use App\Models\CustomQuestion;
use App\Models\JobSkill;
use App\Models\JobStage;
use App\Models\Notification;
use App\Models\Question;
use App\Models\Skill;
use App\Models\Stage;
use App\Models\User;
use App\Models\Interview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class ApplicantController extends Controller
{

    /**
     * 检查用户是否有权限访问指定的申请
     */
    private function canAccessApplicant($applicantId)
    {
        // 如果用户有全局权限，直接返回true
        if (\Auth::user()->can('manage applicant')) {
            return true;
        }
        
        // 获取申请对应的职位
        $applicant = \App\Models\Applicant::findOrFail($applicantId);
        $job = \App\Models\AppliedJob::findOrFail($applicant->job);
        
        // 检查职位是否分配给当前用户
        return $job->isAssignedTo(\Auth::user()->id);
    }

    public function index()
    {
        // 检查用户是否有管理申请人的权限
        if (\Auth::user()->can('manage applicant')) {
            // 有全局权限，显示所有申请
            $stages = Stage::where('parent_id', parentId())->orderBy('order')->get();
            // 清除可能存在的职位限制
            session()->forget('assignedJobIds');
            return view('applicant.index', compact('stages'));
        } else {
            // 无全局权限，仅显示分配给当前用户的职位的申请
            $userId = \Auth::user()->id;
            $assignedJobIds = \Auth::user()->assignedJobs()->pluck('applied_jobs.id')->toArray();
            
            if (empty($assignedJobIds)) {
                return redirect()->route('dashboard')->with('error', __('You don\'t have permission to access any applications.'));
            }
            
            // 保存到session中，以便Stage模型可以使用
            session(['assignedJobIds' => $assignedJobIds]);
            
            // 获取阶段并传递给视图
            $stages = Stage::where('parent_id', parentId())->orderBy('order')->get();
            
            return view('applicant.index', compact('stages', 'assignedJobIds'));
        }
    }


    public function create()
    {
        $jobs = AppliedJob::where('parent_id', parentId())->get()->pluck('job_title', 'id');
        $jobs->prepend(__('Select Job'), '');
        $questions = Question::where('parent_id', parentId())->get();
        $gender = Applicant::$gender;
        return view('applicant.create', compact('jobs', 'questions', 'gender'));
    }


    public function store(Request $request)
    {
        if (\Auth::user()->can('create applicant')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'job' => 'required',
                    'name' => 'required',
                    'email' => 'required',
                    'phone' => 'required',
                    'gender' => 'required',
                    'dob' => 'required',
                    'experience' => 'required',
                    'expected_salary' => 'required',
                    'address' => 'required',
                    'profile' => 'mimes:png,jpeg,jpg,svg,gif',
                    'resume' => 'mimes:pdf,png,jpeg,jpg,svg,doc',
                ]
            );

            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            if (!empty($request->resume)) {
                $resumeFilenameWithExt = $request->file('resume')->getClientOriginalName();
                $resumeFilename = pathinfo($resumeFilenameWithExt, PATHINFO_FILENAME);
                $resumeExtension = $request->file('resume')->getClientOriginalExtension();
                $resumeFileName = $resumeFilename . '_' . time() . '.' . $resumeExtension;
                $directory = storage_path('upload/applicant/resume');
                $filePath = $directory . $resumeFilenameWithExt;
                if (\File::exists($filePath)) {
                    \File::delete($filePath);
                }
                if (!file_exists($directory)) {
                    mkdir($directory, 0777, true);
                }
                $request->file('resume')->storeAs('upload/applicant/resume/', $resumeFileName);
            }

            if (!empty($request->profile)) {
                $profileFilenameWithExt = $request->file('profile')->getClientOriginalName();
                $profileFilename = pathinfo($profileFilenameWithExt, PATHINFO_FILENAME);
                $profileExtension = $request->file('profile')->getClientOriginalExtension();
                $profileFileName = $profileFilename . '_' . time() . '.' . $profileExtension;
                $directory = storage_path('upload/applicant/profile');
                $filePath = $directory . $profileFilenameWithExt;

                if (\File::exists($filePath)) {
                    \File::delete($filePath);
                }
                if (!file_exists($directory)) {
                    mkdir($directory, 0777, true);
                }
                $request->file('profile')->storeAs('upload/applicant/profile/', $profileFileName);
            }

            $applicant = new Applicant();
            $applicant->job = $request->job;
            $applicant->name = $request->name;
            $applicant->email = $request->email;
            $applicant->phone = $request->phone;
            $applicant->profile = !empty($request->profile) ? $profileFileName : '';
            $applicant->resume = !empty($request->resume) ? $resumeFileName : '';
            $applicant->cover_letter = $request->cover_letter;
            $applicant->experience = $request->experience;
            $applicant->expected_salary = $request->expected_salary;
            $applicant->dob = $request->dob;
            $applicant->gender = $request->gender;
            $applicant->address = $request->address;
            $applicant->question = !empty($request->question) ? json_encode($request->question) : null;
            $applicant->parent_id = parentId();
            $applicant->save();

            $module = 'applicant_welcome';
            $notification = Notification::where('parent_id', parentId())->where('module', $module)->first();
            $setting = settings();
            $errorMessage = '';

            if (!empty($notification) && $notification->enabled_email == 1) {
                $notificationResponse = MessageReplace($notification, $applicant->id);

                $data['subject'] = $notificationResponse['subject'];
                $data['message'] = $notificationResponse['message'];
                $data['module'] = $module;
                $data['logo'] = $setting['company_logo'];
                $to = $request->email;

                $response = commonEmailSend($to, $data);

                if ($response['status'] == 'error') {
                    $errorMessage = $response['message'];
                }
            }

            return redirect()->route('applicant.index')->with('success', __('Applicant successfully created.') . '' . $errorMessage);
        } else {
            return redirect()->route('applicant.index')->with('error', __('Permission denied.'));
        }
    }


    public function show($id)
    {
        try {
            // 尝试解密ID（如果加密的）
            if (strpos($id, 'eyJ') === 0) {
                $id = Crypt::decrypt($id);
            }
            
            // 检查用户是否有权限查看此申请
            if (!$this->canAccessApplicant($id)) {
                return redirect()->route('applicant.index')->with('error', __('Permission denied.'));
            }
            
            $applicant = Applicant::find($id);
            
            if (!$applicant) {
                return redirect()->route('applicant.index')->with('error', __('Applicant not found.'));
            }

            // 解析ext_data字段
            if (!empty($applicant->ext_data)) {
                $extData = json_decode($applicant->ext_data, true);
                
                // 处理问题数据
                if (!empty($extData['question'])) {
                    $questionIds = array_keys($extData['question']);
                    
                    // 查询问题内容
                    $questionData = Question::whereIn('id', $questionIds)
                        ->where('parent_id', parentId())
                        ->get()
                        ->keyBy('id');
                    
                    // 构建问题和答案的数组
                    $fullQuestions = [];
                    foreach ($extData['question'] as $questionId => $answer) {
                        $questionText = isset($questionData[$questionId]) ? $questionData[$questionId]->question : "问题 #{$questionId}";
                        $fullQuestions[] = [
                            'id' => $questionId,
                            'question' => $questionText,
                            'answer' => $answer
                        ];
                    }
                    
                    // 将完整的问题数据添加到临时数组中
                    $extData['full_questions'] = $fullQuestions;
                    
                    // 重新赋值整个数组
                    $applicant->ext_data = $extData;
                }
            } else {
                $applicant->ext_data = [];
            }
            
            $stages = Stage::where('parent_id', parentId())->get();
            $questions = Question::where('parent_id', parentId())->get();
            $skills = Skill::where('parent_id', parentId())->get();
            $jobs = AppliedJob::where('parent_id', parentId())->get();
            $meetings = Interview::where(['parent_id' => parentId(), 'applicant' => $id])->orderBy('created_at', 'desc')->get();
            $comment = ApplicantComment::where(['parent_id' => parentId(), 'applicant' => $id])->orderBy('created_at', 'desc')->get();
            
            return view('applicant.show', compact('applicant', 'stages', 'questions', 'skills', 'jobs', 'meetings', 'comment'));
        } catch (\Exception $e) {
            throw $e;
            return redirect()->route('applicant.index')->with('error', __('An error occurred: ') . $e->getMessage());
        }
    }


    public function destroy(Applicant $applicant)
    {
        if (\Auth::user()->can('delete applicant')) {
            $applicant->delete();
            return redirect()->back()->with('success', 'Applicant successfully deleted.');
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function getJobDetails(Request $request)
    {
        $jobDetails = AppliedJob::find($request->id);
        $jobDetails->question = !empty($jobDetails->question) ? explode(',', $jobDetails->question) : '';
        return json_encode($jobDetails);
    }

    public function order(Request $request)
    {
        $data = $request->all();
        foreach ($data['applicantOrder'] as $key => $val) {
            $applicant = Applicant::find($val);
            $applicant->order = $key;
            $applicant->stage = $data['stageId'];
            $applicant->save();
        }
    }

    public function skill(Request $request)
    {
        $applicant = Applicant::find($request->applicant);
        $applicant->skill = implode(',', $request->skill);
        $applicant->save();
    }

    public function rating(Request $request)
    {
        $applicant = Applicant::find($request->applicant);
        $applicant->rating = $request->rating;
        $applicant->save();

        return response()->json($request->rating);
    }

    public function comment(Request $request, $id)
    {
        $userComment = new ApplicantComment();
        $userComment->applicant = $id;
        $userComment->comment = $request->comment;
        $userComment->commented_by = \Auth::user()->id;
        $userComment->parent_id = parentId();
        $userComment->save();
        return redirect()->back()->with('success', __('Applicant comment successfully created.'));
    }

    public function archive()
    {
        if (\Auth::user()->can('manage archive applicant')) {
            $archiveApplicants = Applicant::where('parent_id', parentId())->where('is_archive', 1)->get();
            return view('applicant.archive', compact('archiveApplicants'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function archiveAction($id)
    {
        if (\Gate::check('create archive applicant') || \Gate::check('delete archive applicant')) {
            $applicant = Applicant::find($id);
            if ($applicant->is_archive == 0) {
                $applicant->is_archive = 1;
                $applicant->save();
                return redirect()->route('applicant.archive')->with('success', __('Applicant successfully added to archive list.'));
            } else {
                $applicant->is_archive = 0;
                $applicant->save();
                return redirect()->route('applicant.index')->with('success', __('Applicant successfully remove to archive list.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function hire()
    {
        if (\Auth::user()->can('manage employee')) {
            $hireApplicants = Applicant::where('parent_id', parentId())->where('is_hire', 1)->get();
            return view('applicant.hire', compact('hireApplicants'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function createHire($id)
    {
        return view('applicant.add_hire', compact('id'));
    }

    public function hireAction(Request $request, $id)
    {
        if (\Gate::check('create employee') || \Gate::check('delete employee')) {
            $applicant = Applicant::find($id);
            if ($applicant->is_hire == 0) {
                $applicant->is_hire = 1;
                $applicant->joining_date = $request->joining_date;
                $applicant->ending_date = $request->ending_date;
                $applicant->save();

                $module = 'add_to_employee';
                $notification = Notification::where('parent_id', parentId())->where('module', $module)->first();
                $setting = settings();
                $errorMessage = '';

                if (!empty($notification) && $notification->enabled_email == 1) {
                    $notificationResponse = MessageReplace($notification, $applicant->id);

                    $data['subject'] = $notificationResponse['subject'];
                    $data['message'] = $notificationResponse['message'];
                    $data['module'] = $module;
                    $data['logo'] = $setting['company_logo'];
                    $to = $applicant->email;
                    $response = commonEmailSend($to, $data);

                    if ($response['status'] == 'error') {
                        $errorMessage = $response['message'];
                    }
                }

                return redirect()->route('applicant.hire')->with('success', __('Applicant successfully added to employee list.') . '</br>' . $errorMessage);
            } else {
                $applicant->is_hire = 0;
                $applicant->save();
                return redirect()->route('applicant.index')->with('success', __('Applicant successfully remove to employee list.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
}
