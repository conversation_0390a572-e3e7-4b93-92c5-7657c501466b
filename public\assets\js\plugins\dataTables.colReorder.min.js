/*! ColReorder 2.0.0-dev
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var r,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(r=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||r(t),n(t,e),o(e,t,t.document)}:(n(window,r),module.exports=o(r,window,window.document))):o(jQuery,window,document)}(function(t,o,e){"use strict";var r,n,s,t=t.fn.dataTable;return r=function(c,t,f){var n=c.fn.dataTable;function h(t,e,o,r){var n=t.splice(e,o);n.unshift(0),n.unshift(r<e?r:r-o+1),t.splice.apply(t,n)}function a(t){t.rows().invalidate("data"),t.column(0).visible(t.column(0).visible()),t.columns.adjust();var e=t.colReorder.order();t.trigger("columns-reordered",[{order:e,mapping:g(e)}])}function s(t){return t.settings()[0].aoColumns.map(function(t){return t._crOriginalIdx})}function p(t,e,o,r){for(var n=[],s=0;s<t.length;s++){var i=t[s];h(i,o[0],o.length,r);for(var a=0;a<i.length;a++){var l,u=i[a].cell;n.includes(u)||(l=u.getAttribute("data-dt-column").split(",").map(function(t){return e[t]}).join(","),u.setAttribute("data-dt-column",l),n.push(u))}}}function i(t){t.columns().iterator("column",function(t,e){t=t.aoColumns;void 0===t[e]._crOriginalIdx&&(t[e]._crOriginalIdx=e)})}function g(t){for(var e=[],o=0;o<t.length;o++)e[t[o]]=o;return e}function l(t,e,o){var r,n=t.settings()[0],s=n.aoColumns,i=s.map(function(t,e){return e});if(!e.includes(o)){h(i,e[0],e.length,o);var a=g(i);for(h(s,e[0],e.length,o),r=0;r<n.aoData.length;r++){var l=n.aoData[r],u=l.anCells;if(u)for(h(u,e[0],e.length,o),c=0;c<u.length;c++)l.nTr&&u[c]&&s[c].bVisible&&l.nTr.appendChild(u[c]),u[c]&&u[c]._DT_CellIndex&&(u[c]._DT_CellIndex.column=c)}for(r=0;r<s.length;r++){for(var d=s[r],c=0;c<d.aDataSort.length;c++)d.aDataSort[c]=a[d.aDataSort[c]];d.idx=a[d.idx],d.bVisible&&n.colgroup.append(d.colEl)}p(n.aoHeader,a,e,o),p(n.aoFooter,a,e,o),h(n.aoPreSearchCols,e[0],e.length,o),m(a,n.aaSorting),Array.isArray(n.aaSortingFixed)?m(a,n.aaSortingFixed):(n.aaSortingFixed.pre||n.aaSortingFixed.post)&&m(a,n.aaSortingFixed.pre),n.aLastSort.forEach(function(t){t.src=a[t.src]}),t.trigger("column-reorder",[t.settings()[0],{from:e,to:o,mapping:a}])}}function m(t,e){for(var o=0;o<e.length;o++){var r=e[o];"number"==typeof r?e[o]=t[r]:c.isPlainObject(r)&&void 0!==r.idx?r.idx=t[r.idx]:Array.isArray(r)&&"number"==typeof r[0]&&(r[0]=t[r[0]])}}function u(t,e,o){var r=!1;if(e.length!==t.columns().count())t.error("ColReorder - column count mismatch");else{for(var n=g(e=o?d(t,e,"toCurrent"):e),s=0;s<n.length;s++){var i=n.indexOf(s);s!==i&&(h(n,i,1,s),l(t,[i],s),r=!0)}r&&a(t)}}function d(t,e,o){var r=t.colReorder.order(),n=t.settings()[0].aoColumns;return"toCurrent"===o||"fromOriginal"===o?Array.isArray(e)?e.map(function(t){return r.indexOf(t)}):r.indexOf(e):Array.isArray(e)?e.map(function(t){return n[t]._crOriginalIdx}):n[e]._crOriginalIdx}function v(t,e,o){var r=t.columns().count();return!(e[0]<o&&o<e[e.length]||e[0]<0&&e[e.length-1]>r||o<0&&r<o||!e.includes(o)&&(!y(t.table().header.structure(),e,o)||!y(t.table().footer.structure(),e,o)))}function y(t,e,o){for(var r=function(t){for(var e=[],o=0;o<t.length;o++){e.push([]);for(var r=0;r<t[o].length;r++){var n=t[o][r];if(n)for(var s=0;s<n.rowspan;s++)for(var i=0;i<n.colspan;i++)e[o+s][r+i]=n.cell}}return e}(t),n=0;n<r.length;n++)h(r[n],e[0],e.length,o);for(n=0;n<r.length;n++)for(var s=[],i=0;i<r[n].length;i++){var a=r[n][i];if(s.includes(a)){if(s[s.length-1]!==a)return}else s.push(a)}return 1}_.prototype.disable=function(){return this.c.enable=!1,this},_.prototype.enable=function(t){return!1===(t=void 0===t?!0:t)?this.disable():(this.c.enable=!0,this)},_.prototype._addListener=function(t){var e=this;c(t).on("selectstart.colReorder",function(){return!1}).on("mousedown.colReorder touchstart.colReorder",function(t){"mousedown"===t.type&&1!==t.which||e.c.enable&&e._mouseDown(t,this)})},_.prototype._createDragNode=function(){var t=this.s.mouse.target,e=t.parent(),o=e.parent(),r=o.parent(),n=t.clone();this.dom.drag=c(r[0].cloneNode(!1)).addClass("dtcr-cloned").append(c(o[0].cloneNode(!1)).append(c(e[0].cloneNode(!1)).append(n[0]))).css({position:"absolute",top:0,left:0,width:c(t).outerWidth(),height:c(t).outerHeight()}).appendTo("body")},_.prototype._cursorPosition=function(t,e){return(-1!==t.type.indexOf("touch")?t.originalEvent.touches[0]:t)[e]},_.prototype._mouseDown=function(t,e){for(var o=this,r=c(t.target).closest("th, td"),n=r.offset(),s=this.dt.columns(this.c.columns).indexes().toArray(),i=c(e).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),a=0;a<i.length;a++)if(!s.includes(i[a]))return!1;this.s.mouse.start.x=this._cursorPosition(t,"pageX"),this.s.mouse.start.y=this._cursorPosition(t,"pageY"),this.s.mouse.offset.x=this._cursorPosition(t,"pageX")-n.left,this.s.mouse.offset.y=this._cursorPosition(t,"pageY")-n.top,this.s.mouse.target=r,this.s.mouse.targets=i;for(var l=0;l<i.length;l++){var u=this.dt.cells(null,i[l],{page:"current"}).nodes().to$(),d="dtcr-moving";0===l&&(d+=" dtcr-moving-first"),l===i.length-1&&(d+=" dtcr-moving-last"),u.addClass(d)}this._regions(i),this._scrollRegions(),c(f).on("mousemove.colReorder touchmove.colReorder",function(t){o._mouseMove(t)}).on("mouseup.colReorder touchend.colReorder",function(t){o._mouseUp(t)})},_.prototype._mouseMove=function(t){if(null===this.dom.drag){if(Math.pow(Math.pow(this._cursorPosition(t,"pageX")-this.s.mouse.start.x,2)+Math.pow(this._cursorPosition(t,"pageY")-this.s.mouse.start.y,2),.5)<5)return;c(f.body).addClass("dtcr-dragging"),this._createDragNode()}this.dom.drag.css({left:this._cursorPosition(t,"pageX")-this.s.mouse.offset.x,top:this._cursorPosition(t,"pageY")-this.s.mouse.offset.y});var e=c(this.dt.table().node()).offset().left,o=this._cursorPosition(t,"pageX")-e,e=this.s.dropZones.find(function(t){return t.left<=o&&o<=t.left+t.width});this.s.mouse.absLeft=this._cursorPosition(t,"pageX"),e&&!e.self&&this._move(e,o)},_.prototype._mouseUp=function(t){c(f).off(".colReorder"),c(f.body).removeClass("dtcr-dragging"),this.dom.drag&&(this.dom.drag.remove(),this.dom.drag=null),this.s.scrollInterval&&clearInterval(this.s.scrollInterval),this.dt.cells(".dtcr-moving").nodes().to$().removeClass("dtcr-moving dtcr-moving-first dtcr-moving-last")},_.prototype._move=function(t,e){var o,r,n=this,t=(this.dt.colReorder.move(this.s.mouse.targets,t.colIdx),this.s.mouse.targets=c(this.s.mouse.target).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),this._regions(this.s.mouse.targets),this.s.dropZones.find(function(t){return t.colIdx===n.s.mouse.targets[0]})),s=this.s.dropZones.indexOf(t);t.left>e&&(r=t.left-e,o=this.s.dropZones[s-1],t.left-=r,t.width+=r,o)&&(o.width-=r),(t=this.s.dropZones.find(function(t){return t.colIdx===n.s.mouse.targets[n.s.mouse.targets.length-1]})).left+t.width<e&&(o=e-(t.left+t.width),r=this.s.dropZones[s+1],t.width+=o,r)&&(r.left+=o,r.width-=o)},_.prototype._regions=function(n){var s=this,i=[],a=0,l=0,u=this.dt.columns(this.c.columns).indexes().toArray(),d=this.dt.columns().widths();this.dt.columns().every(function(t,e,o){var r;this.visible()&&(r=d[t],u.includes(t)&&(v(s.dt,n,t)?i.push({colIdx:t,left:a-l,self:n[0]<=t&&t<=n[n.length-1],width:r+l}):t<n[0]?i.length&&(i[i.length-1].width+=r):t>n[n.length-1]&&(l+=r)),a+=r)}),this.s.dropZones=i},_.prototype._isScrolling=function(){return this.dt.table().body().parentNode!==this.dt.table().header().parentNode},_.prototype._scrollRegions=function(){var e,o,r,n;this._isScrolling()&&(o=c((e=this).dt.table().container()).position().left,r=c(this.dt.table().container()).outerWidth(),n=this.dt.table().body().parentElement.parentElement,this.s.scrollInterval=setInterval(function(){var t=e.s.mouse.absLeft;t<o+75&&n.scrollLeft?n.scrollLeft-=5:o+r-75<t&&n.scrollLeft<n.scrollWidth&&(n.scrollLeft+=5)},25))},_.defaults={columns:"",enable:!0,order:null},_.version="2.0.0-dev";
/*! ColReorder 2.0.0-dev
 * © SpryMedia Ltd - datatables.net/license
 */var b=_;function _(r,t){this.dom={drag:null},this.c={columns:null,enable:null,order:null},this.s={dropZones:[],mouse:{absLeft:-1,offset:{x:-1,y:-1},start:{x:-1,y:-1},target:null,targets:[]},scrollInterval:null};var e,o=this;r.settings()[0]._colReorder||((r.settings()[0]._colReorder=this).dt=r,c.extend(this.c,_.defaults,t),i(r),r.on("stateSaveParams",function(t,e,o){o.colReorder=s(r)}),r.on("destroy",function(){r.off(".colReorder"),r.colReorder.reset()}),t=r.state.loaded(),e=this.c.order,(e=t&&t.colReorder?t.colReorder:e)&&r.ready(function(){u(r,e,!0)}),r.table().header.structure().forEach(function(t){for(var e=0;e<t.length;e++)t[e]&&t[e].cell&&o._addListener(t[e].cell)}))}return n.Api.register("colReorder.enable()",function(e){return this.iterator("table",function(t){t._colReorder&&t._colReorder.enable(e)})}),n.Api.register("colReorder.disable()",function(){return this.iterator("table",function(t){t._colReorder&&t._colReorder.disable()})}),n.Api.register("colReorder.move()",function(t,e){return i(this),v(this,t=Array.isArray(t)?t:[t],e)?this.tables().every(function(){l(this,t,e),a(this)}):(this.error("ColReorder - invalid move"),this)}),n.Api.register("colReorder.order()",function(t,e){return i(this),t?this.tables().every(function(){u(this,t,e)}):this.context.length?s(this):null}),n.Api.register("colReorder.reset()",function(){return i(this),this.tables().every(function(){var t=this.columns().every(function(t){return t}).flatten().toArray();u(this,t,!0)})}),n.Api.register("colReorder.transpose()",function(t,e){return i(this),d(this,t,e=e||"toCurrent")}),n.ColReorder=b,c(f).on("stateLoadInit.dt",function(t,e,o){if("dt"===t.namespace){t=new n.Api(e);if(o.colReorder)if(t.ready())u(t,o.colReorder,!0);else{m(g(o.colReorder),o.order);for(var r=0;r<o.columns.length;r++)o.columns[r]._cr_sort=o.colReorder[r];o.columns.sort(function(t,e){return t._cr_sort-e._cr_sort})}}}),c(f).on("preInit.dt",function(t,e){var o,r;"dt"===t.namespace&&(t=e.oInit.colReorder,r=n.defaults.colReorder,t||r)&&(o=c.extend({},r,t),!1!==t)&&(r=new n.Api(e),new b(r,o))}),n},"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return r(t,0,e)}):"object"==typeof exports?(n=require("jquery"),s=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},void 0===o?module.exports=function(t,e){return t=t||o,e=e||n(t),s(t,e),r(e,0,t.document)}:(s(o,n),module.exports=r(n,0,o.document))):r(jQuery,0,e),t});