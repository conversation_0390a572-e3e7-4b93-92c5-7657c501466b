<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\Interview;
use App\Models\Notification;
use App\Models\Stage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class InterviewController extends Controller
{

    public function index()
    {
        if (\Auth::user()->can('manage interview')) {
            if (\Auth::user()->can('manage interview')) {
                $interviews = Interview::where('parent_id', parentId())->get();
                return view('interview.index', compact('interviews'));
            } else {
                return redirect()->back()->with('error', __('Permission Denied!'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        $users = User::where('parent_id', parentId())->orWhere('id', \Auth::user()->id)->get()->pluck('name', 'id');
        $users->prepend(__('Select User'), '');

        $applicants = Applicant::where('parent_id', parentId())->get()->pluck('name', 'id');
        $applicants->prepend(__('Select Applicant'), '');

        return view('interview.create', compact('users', 'applicants'));
    }


    public function store(Request $request)
    {
        if (\Auth::user()->can('create interview')) {
            $validator = \Validator::make(
                $request->all(), [
                    'applicant' => 'required',
                    'interview_date' => 'required',
                    'interview_start_time' => 'required',
                    'interview_end_time' => 'required',
                    'total_duration' => 'required',
                    'assign_user' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $interview = new Interview();
            $interview->applicant = $request->applicant;
            $interview->interview_date = $request->interview_date;
            $interview->interview_start_time = $request->interview_start_time;
            $interview->interview_end_time = $request->interview_end_time;
            $interview->total_duration = $request->total_duration;
            $interview->assign_user = $request->assign_user;
            $interview->notes = $request->notes;
            $interview->parent_id = parentId();
            $interview->save();

            // 面试创建通知 - 发送邮件给应聘者
            $module = 'interview_create';
            $notification = Notification::where('parent_id', parentId())->where('module', $module)->first();
            $setting = settings();
            $errorMessage = '';

            // 检查是否启用了邮件通知
            if (!empty($notification) && $notification->enabled_email == 1) {
                // 获取替换后的消息内容
                $notificationResponse = MessageReplace($notification, $interview->id);

                // 准备邮件数据
                $data['subject'] = $notificationResponse['subject'];
                $data['message'] = $notificationResponse['message'];
                $data['module'] = $module;
                $data['logo'] = $setting['company_logo'];
                
                // 获取应聘者邮箱
                $to = $interview->applicants->email;
                
                // 发送邮件
                $response = commonEmailSend($to, $data);

                if ($response['status'] == 'error') {
                    $errorMessage = $response['message'];
                }
            }

            // 面试分配通知 - 发送邮件给面试官
            $module = 'assign_applicant';
            $notification = Notification::where('parent_id', parentId())->where('module', $module)->first();
            $setting = settings();
            
            // 检查是否启用了邮件通知
            if (!empty($notification) && $notification->enabled_email == 1) {
                // 获取替换后的消息内容
                $notificationResponse = MessageReplace($notification, $interview->id);

                // 准备邮件数据
                $data['subject'] = $notificationResponse['subject'];
                $data['message'] = $notificationResponse['message'];
                $data['module'] = $module;
                $data['logo'] = $setting['company_logo'];
                
                // 获取面试官邮箱
                $to = $interview->users->email;

                // 发送邮件
                $response = commonEmailSend($to, $data);

                if ($response['status'] == 'error') {
                    if (!empty($errorMessage)) {
                        $errorMessage .= '<br>';
                    }
                    $errorMessage .= $response['message'];
                }
            }

            return redirect()->back()->with('success', __('Applicant interview successfully created.').'</br>'.$errorMessage);
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function show(Interview $interview)
    {
        return view('interview.show', compact('interview'));
    }


    public function edit(Interview $interview)
    {
        $users = User::where('parent_id', parentId())->orWhere('id', \Auth::user()->id)->get()->pluck('name', 'id');
        $users->prepend(__('Select User'), '');

        $applicants = Applicant::where('parent_id', parentId())->get()->pluck('name', 'id');
        $applicants->prepend(__('Select Applicant'), '');

        return view('interview.edit', compact('users', 'applicants', 'interview'));
    }


    public function update(Request $request, Interview $interview)
    {
        if (\Auth::user()->can('edit interview')) {
            $validator = \Validator::make(
                $request->all(), [
                    'applicant' => 'required',
                    'interview_date' => 'required',
                    'interview_start_time' => 'required',
                    'interview_end_time' => 'required',
                    'total_duration' => 'required',
                    'assign_user' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $interview->applicant = $request->applicant;
            $interview->interview_date = $request->interview_date;
            $interview->interview_start_time = $request->interview_start_time;
            $interview->interview_end_time = $request->interview_end_time;
            $interview->total_duration = $request->total_duration;
            $interview->assign_user = $request->assign_user;
            $interview->notes = $request->notes;
            $interview->save();
            return redirect()->back()->with('success', __('Applicant interview successfully updated.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function destroy(Interview $interview)
    {
        $interview->delete();
        return redirect()->back()->with('success', __('Applicant interview successfully deleted.'));
    }

    public function todayList()
    {
        if (\Auth::user()->can('manage interview')) {
            $interviews = Interview::where('parent_id', parentId())->where('interview_date',date('Y-m-d'))->get();
            return view('interview.today', compact('interviews'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }

}
