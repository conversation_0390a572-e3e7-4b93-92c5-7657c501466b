// Variables
@import "variables";

//@extend-elements
//original selectors
//sub, sup
.extend_1
	font-size: 75%
	line-height: 0
	position: relative
	vertical-align: baseline


//original selectors
//button, input, optgroup, select, textarea
.extend_2
	color: inherit
	font: inherit
	margin: 0


@import $url_0;
html
	font-family: $font_1
	-ms-text-size-adjust: 100%
	-webkit-text-size-adjust: 100%
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 300
	color: $color_3
	font-size: 12px
	line-height: 1.75em
	input[type=button]
		-webkit-appearance: button
		cursor: pointer

	input[disabled]
		cursor: default


body
	margin: 0
	text-rendering: optimizeLegibility
	-webkit-font-smoothing: antialiased
	-moz-osx-font-smoothing: grayscale
	-moz-font-feature-settings: "liga" on

article
	display: block

aside
	display: block

details
	display: block

figcaption
	display: block

figure
	display: block
	margin: 1em 40px

footer
	display: block

header
	display: block

hgroup
	display: block

main
	display: block

menu
	display: block

nav
	display: block

section
	display: block

summary
	display: block

audio
	display: inline-block
	vertical-align: baseline
	&:not([controls])
		display: none
		height: 0


canvas
	display: inline-block
	vertical-align: baseline

progress
	display: inline-block
	vertical-align: baseline

video
	display: inline-block
	vertical-align: baseline

[hidden]
	display: none

template
	display: none

a
	background-color: transparent
	text-decoration: none
	color: $color_5
	//If you use compass, instead of the line below you could use + transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
	transition: all .2s
	margin: 0
	padding: 0
	&:active
		outline: 0

	&:hover
		outline: 0
		color: $color_6


abbr[title]
	border-bottom: 1px dotted

b
	font-weight: 700
	margin: 0
	padding: 0

strong
	font-weight: 700
	margin: 0
	padding: 0

dfn
	font-style: italic
	margin: 0
	padding: 0

h1
	font-size: 2em
	margin: .67em 0
	font-size: 27.85438995234061px
	margin-top: .942400822452556em
	line-height: 1.130880986943067em
	margin-bottom: .188480164490511em
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both

mark
	background: $color_0
	color: $color_1

small
	font-size: 80%
	margin: 0
	padding: 0
	line-height: 0

sub
	@extend %extend_1
	bottom: -.25em
	margin: 0
	padding: 0
	line-height: 0

sup
	@extend %extend_1
	top: -.5em
	margin: 0
	padding: 0
	line-height: 0

img
	border: 0
	margin: 0
	padding: 0

hr
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: content-box
	height: 0

pre
	overflow: auto
	padding: .875em
	margin-bottom: 1.75em
	font-family: $font_2
	font-size: 1em
	margin: 0
	padding: 0
	margin-bottom: 1.75em
	code
		padding: 0


code
	font-family: $font_2
	font-size: 1em
	margin: 0
	padding: 0
	font-family: $font_7, $font_8, $font_9, $font_10, $font_2
	padding: .0875em .2625em
	line-height: 0

kbd
	font-family: $font_2
	font-size: 1em
	margin: 0
	padding: 0

samp
	font-family: $font_2
	font-size: 1em
	margin: 0
	padding: 0

button
	@extend %extend_2
	overflow: visible
	text-transform: none
	-webkit-appearance: button
	cursor: pointer
	display: block
	cursor: pointer
	font-size: 12px
	padding: .4375em 1.75em
	margin-bottom: 1.18125em

input
	@extend %extend_2
	line-height: normal

optgroup
	@extend %extend_2
	font-weight: 700

select
	@extend %extend_2
	text-transform: none

textarea
	@extend %extend_2
	overflow: auto
	display: block
	max-width: 100%
	padding: .4375em
	font-size: 12px
	margin-bottom: 1.18125em

input[type=reset]
	-webkit-appearance: button
	cursor: pointer

input[type=submit]
	-webkit-appearance: button
	cursor: pointer
	display: block
	cursor: pointer
	font-size: 12px
	padding: .4375em 1.75em
	margin-bottom: 1.18125em

button[disabled]
	cursor: default

button::-moz-focus-inner
	border: 0
	padding: 0

input::-moz-focus-inner
	border: 0
	padding: 0

input[type=checkbox]
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: border-box
	padding: 0

input[type=radio]
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: border-box
	padding: 0

input[type=number]::-webkit-inner-spin-button
	height: auto

input[type=number]::-webkit-outer-spin-button
	height: auto

input[type=search]
	-webkit-appearance: textfield
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: content-box

input[type=search]::-webkit-search-cancel-button
	-webkit-appearance: none

input[type=search]::-webkit-search-decoration
	-webkit-appearance: none

fieldset
	border: 1px solid $color_2
	margin: 0 2px
	padding: .35em .625em .75em
	padding: .875em 1.75em 1.75em
	border-width: 1px
	border-style: solid
	max-width: 100%
	margin-bottom: 1.8375em
	margin: 0
	padding: 0
	button
		margin-bottom: 0

	input[type=submit]
		margin-bottom: 0


legend
	border: 0
	padding: 0
	color: $color_4
	font-weight: 700
	margin: 0
	padding: 0

table
	width: 100%
	border-spacing: 0
	border-collapse: collapse
	margin-bottom: 2.1875em
	margin: 0
	padding: 0
	margin-bottom: 1.75em

td
	padding: 0
	margin: 0
	padding: 0
	padding: .21875em .875em

th
	padding: 0
	margin: 0
	padding: 0
	text-align: left
	color: $color_4
	padding: .21875em .875em

@font-face
	font-family: $font_0
	src: $url_1
	src: $url_2 format("embedded-opentype"), $url_3 format("truetype"), $url_4 format("woff"), $url_5 format("svg")
	font-weight: 400
	font-style: normal

@media(min-width:600px)
	html
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	h1
		font-size: calc(27.85438995234061px +18.56959 *((100vw - 600px) / 540))

	h2
		font-size: calc(23.53700340860508px +15.69134 *((100vw - 600px) / 540))

	h3
		font-size: calc(19.888804974891777px +13.2592 *((100vw - 600px) / 540))

	h4
		font-size: calc(16.806071548796314px +11.20405 *((100vw - 600px) / 540))

	h5
		font-size: calc(14.201156945318074px +9.46744 *((100vw - 600px) / 540))

	h6
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	input[type=email]
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	input[type=password]
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	input[type=text]
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	textarea
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	button
		font-size: calc(12px +8 *((100vw - 600px) / 540))

	input[type=submit]
		font-size: calc(12px +8 *((100vw - 600px) / 540))


@media(min-width:1140px)
	html
		font-size: 20px

	h1
		font-size: 46.423983253901014px
		margin-top: .942400822452556em
		line-height: 1.130880986943067em
		margin-bottom: .188480164490511em

	h2
		font-size: 39.228339014341806px
		margin-top: 1.115265165420465em
		line-height: 1.338318198504558em
		margin-bottom: .240111086421698em

	h3
		font-size: 33.14800829148629px
		margin-top: 1.319837970815179em
		line-height: 1.583805564978215em
		margin-bottom: .287857499569283em

	h4
		font-size: 28.01011924799386px
		margin-top: 1.561935513828041em
		line-height: 1.87432261659365em
		margin-bottom: .345845057728222em

	h5
		font-size: 23.668594908863454px
		margin-top: 1.84844094752817em
		line-height: 2.218129137033805em
		margin-bottom: .369688189505634em

	h6
		font-size: 20px
		margin-top: 2.1875em
		line-height: 2.625em
		margin-bottom: .473958333333333em

	fieldset
		margin-bottom: 2.078125em

	input[type=email]
		font-size: 20px
		margin-bottom: .5140625em

	input[type=password]
		font-size: 20px
		margin-bottom: .5140625em

	input[type=text]
		font-size: 20px
		margin-bottom: .5140625em

	textarea
		font-size: 20px
		margin-bottom: .5140625em

	button
		font-size: 20px
		margin-bottom: 1.3125em

	input[type=submit]
		font-size: 20px
		margin-bottom: 1.3125em

	table
		margin-bottom: 2.05625em

	th
		padding: .4375em .875em

	td
		padding: .4375em .875em


abbr
	margin: 0
	padding: 0
	border-bottom: 1px dotted currentColor
	cursor: help

acronym
	margin: 0
	padding: 0
	border-bottom: 1px dotted currentColor
	cursor: help

address
	margin: 0
	padding: 0
	margin-bottom: 1.75em
	font-style: normal

big
	margin: 0
	padding: 0
	line-height: 0

blockquote
	margin: 0
	padding: 0
	margin-bottom: 1.75em
	font-style: italic
	cite
		display: block
		font-style: normal


caption
	margin: 0
	padding: 0

center
	margin: 0
	padding: 0

cite
	margin: 0
	padding: 0

dd
	margin: 0
	padding: 0

del
	margin: 0
	padding: 0

dl
	margin: 0
	padding: 0
	margin-bottom: 1.75em

dt
	margin: 0
	padding: 0
	color: $color_4
	font-weight: 700

em
	margin: 0
	padding: 0

form
	margin: 0
	padding: 0

h2
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both
	font-size: 23.53700340860508px
	margin-top: 1.115265165420465em
	line-height: 1.338318198504558em
	margin-bottom: .251483121980101em

h3
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both
	font-size: 19.888804974891777px
	margin-top: 1.319837970815179em
	line-height: 1.583805564978215em
	margin-bottom: .303784103173448em

h4
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both
	font-size: 16.806071548796314px
	margin-top: 1.561935513828041em
	line-height: 1.87432261659365em
	margin-bottom: .368150361036632em

h5
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both
	font-size: 14.201156945318074px
	margin-top: 1.84844094752817em
	line-height: 2.218129137033805em
	margin-bottom: .369688189505634em

h6
	margin: 0
	padding: 0
	font-family: $font_3, $font_4, $font_5, $font_6, $font_1
	font-weight: 500
	color: $color_4
	clear: both
	font-size: 12px
	margin-top: 2.1875em
	line-height: 2.625em
	margin-bottom: .619791666666667em

i
	margin: 0
	padding: 0

ins
	margin: 0
	padding: 0

label
	margin: 0
	padding: 0
	display: block
	padding-bottom: .21875em
	margin-bottom: -.21875em

li
	margin: 0
	padding: 0

ol
	margin: 0
	padding: 0
	margin-bottom: 1.75em
	padding-left: 1.4em

p
	margin: 0
	padding: 0
	margin-bottom: 1.75em

q
	margin: 0
	padding: 0

s
	margin: 0
	padding: 0

strike
	margin: 0
	padding: 0

tbody
	margin: 0
	padding: 0

tfoot
	margin: 0
	padding: 0

thead
	margin: 0
	padding: 0

tr
	margin: 0
	padding: 0

tt
	margin: 0
	padding: 0

u
	margin: 0
	padding: 0

ul
	margin: 0
	padding: 0
	margin-bottom: 1.75em
	padding-left: 1.1em

var
	margin: 0
	padding: 0

input[type=email]
	display: block
	max-width: 100%
	padding: .4375em
	font-size: 12px
	margin-bottom: 1.18125em

input[type=password]
	display: block
	max-width: 100%
	padding: .4375em
	font-size: 12px
	margin-bottom: 1.18125em

input[type=text]
	display: block
	max-width: 100%
	padding: .4375em
	font-size: 12px
	margin-bottom: 1.18125em

.master
	background-image: $url_6
	background-size: cover
	background-position: top
	min-height: 100vh
	display: -webkit-flex
	display: -ms-flexbox
	display: flex
	-webkit-justify-content: center
	-ms-flex-pack: center
	justify-content: center
	-webkit-align-items: center
	-ms-flex-align: center
	align-items: center

.box
	width: 450px
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 0 0 3px 3px
	overflow: hidden
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: border-box
	//If you use compass, instead of the line below you could use + box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
	box-shadow: 0 10px 10px $color_7, 0 6px 3px $color_8

.header
	background-color: $color_9
	padding: 30px 30px 40px
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 3px 3px 0 0
	text-align: center

.header__step
	font-weight: 300
	text-transform: uppercase
	font-size: 14px
	letter-spacing: 1.1px
	margin: 0 0 10px
	-webkit-user-select: none
	-moz-user-select: none
	-ms-user-select: none
	//If you use compass, instead of the line below you could use + user-select($select)
	user-select: none
	color: $color_10

.header__title
	-webkit-user-select: none
	-moz-user-select: none
	-ms-user-select: none
	//If you use compass, instead of the line below you could use + user-select($select)
	user-select: none
	color: $color_10
	font-weight: 400
	font-size: 20px
	margin: 0 0 15px

.step
	padding-left: 0
	list-style: none
	margin-bottom: 0
	display: -webkit-flex
	display: -ms-flexbox
	display: flex
	-webkit-flex-direction: row-reverse
	-ms-flex-direction: row-reverse
	flex-direction: row-reverse
	-webkit-justify-content: center
	-ms-flex-pack: center
	justify-content: center
	-webkit-align-items: center
	-ms-flex-align: center
	align-items: center
	margin-top: -20px

.step__divider
	background-color: $color_11
	-webkit-user-select: none
	-moz-user-select: none
	-ms-user-select: none
	//If you use compass, instead of the line below you could use + user-select($select)
	user-select: none
	width: 60px
	height: 3px
	&:first-child
		-webkit-flex: 1 0 auto
		-ms-flex: 1 0 auto
		flex: 1 0 auto

	&:last-child
		-webkit-flex: 1 0 auto
		-ms-flex: 1 0 auto
		flex: 1 0 auto


.step__icon
	background-color: $color_11
	font-style: normal
	width: 40px
	height: 40px
	display: -webkit-flex
	display: -ms-flexbox
	display: flex
	-webkit-justify-content: center
	-ms-flex-pack: center
	justify-content: center
	-webkit-align-items: center
	-ms-flex-align: center
	align-items: center
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 50%
	color: $color_10
	&.welcome:before
		content: '\f144'
		font-family: $font_0

	&.requirements:before
		content: '\f127'
		font-family: $font_0

	&.permissions:before
		content: '\f296'
		font-family: $font_0

	&.database:before
		content: '\f454'
		font-family: $font_0

	&.update:before
		content: '\f2bf'
		font-family: $font_0


.main
	margin-top: -20px
	background-color: $color_10
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 0 0 3px 3px
	padding: 40px 40px 30px

.buttons
	text-align: center

.buttons--right
	text-align: right

.button
	display: inline-block
	background-color: $color_12
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 2px
	padding: 7px 20px
	color: $color_10
	//If you use compass, instead of the line below you could use + box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
	box-shadow: 0 1px 1.5px $color_13, 0 1px 1px $color_14
	text-decoration: none
	outline: none
	border: none
	//If you use compass, instead of the line below you could use + transition($transition-1, $transition-2, $transition-3, $transition-4, $transition-5, $transition-6, $transition-7, $transition-8, $transition-9, $transition-10)
	transition: box-shadow .2s ease, background-color .2s ease
	&:hover
		color: $color_10
		//If you use compass, instead of the line below you could use + box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
		box-shadow: 0 10px 10px $color_7, 0 6px 3px $color_8
		background-color: $color_15


.button--light
	padding: 3px 16px
	font-size: 16px
	border-top: 1px solid $color_16
	color: $color_17
	background: $color_10
	&:hover
		color: $color_17
		background: $color_10
		//If you use compass, instead of the line below you could use + box-shadow($shadow-1, $shadow-2, $shadow-3, $shadow-4, $shadow-5, $shadow-6, $shadow-7, $shadow-8, $shadow-9, $shadow-10)
		box-shadow: 0 3px 3px $color_18, 0 3px 3px $color_8


.list
	padding-left: 0
	list-style: none
	margin-bottom: 0
	margin: 20px 0 35px
	border: 1px solid $color_13
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 2px

.list__item
	position: relative
	overflow: hidden
	padding: 7px 20px
	border-bottom: 1px solid $color_13
	&:first-letter
		text-transform: uppercase

	&:last-child
		border-bottom: none

	&:before
		display: -webkit-flex
		display: -ms-flexbox
		display: flex
		-webkit-justify-content: center
		-ms-flex-pack: center
		justify-content: center
		-webkit-align-items: center
		-ms-flex-align: center
		align-items: center
		padding: 7px 20px
		position: absolute
		top: 0
		right: 0
		bottom: 0

	&.success:before
		color: $color_19
		content: '\f120'
		font-family: $font_0

	&.error:before
		color: $color_20
		content: '\f128'
		font-family: $font_0


.list__item--permissions
	&:before
		content: ''!important

	span
		display: -webkit-flex
		display: -ms-flexbox
		display: flex
		-webkit-justify-content: center
		-ms-flex-pack: center
		justify-content: center
		-webkit-align-items: center
		-ms-flex-align: center
		align-items: center
		padding: 7px 20px
		position: absolute
		top: 0
		right: 0
		bottom: 0
		background-color: $color_21
		font-weight: 700
		font-size: 16px
		&:before
			margin-right: 7px
			font-weight: 400


	&.success span:before
		color: $color_19
		content: '\f120'
		font-family: $font_0

	&.error span:before
		color: $color_20
		content: '\f128'
		font-family: $font_0


.textarea
	//If you use compass, instead of the line below you could use + box-sizing($bs)
	box-sizing: border-box
	width: 100%
	font-size: 14px
	line-height: 25px
	height: 150px
	outline: none
	border: 1px solid $color_22
	~ .button
		margin-bottom: 35px


.alert
	margin: 0 0 10px
	font-weight: 700
	font-size: 16px
	background-color: $color_21
	//If you use compass, instead of the line below you could use + border-radius($radius, $vertical-radius)
	border-radius: 2px
	padding: 0 10px

svg:not(:root)
	overflow: hidden

.ion-alert:before
	content: "\f101"

.ion-alert-circled:before
	content: "\f100"

.ion-android-add:before
	content: "\f2c7"

.ion-android-add-circle:before
	content: "\f359"

.ion-android-alarm-clock:before
	content: "\f35a"

.ion-android-alert:before
	content: "\f35b"

.ion-android-apps:before
	content: "\f35c"

.ion-android-archive:before
	content: "\f2c9"

.ion-android-arrow-back:before
	content: "\f2ca"

.ion-android-arrow-down:before
	content: "\f35d"

.ion-android-arrow-dropdown:before
	content: "\f35f"

.ion-android-arrow-dropdown-circle:before
	content: "\f35e"

.ion-android-arrow-dropleft:before
	content: "\f361"

.ion-android-arrow-dropleft-circle:before
	content: "\f360"

.ion-android-arrow-dropright:before
	content: "\f363"

.ion-android-arrow-dropright-circle:before
	content: "\f362"

.ion-android-arrow-dropup:before
	content: "\f365"

.ion-android-arrow-dropup-circle:before
	content: "\f364"

.ion-android-arrow-forward:before
	content: "\f30f"

.ion-android-arrow-up:before
	content: "\f366"

.ion-android-attach:before
	content: "\f367"

.ion-android-bar:before
	content: "\f368"

.ion-android-bicycle:before
	content: "\f369"

.ion-android-boat:before
	content: "\f36a"

.ion-android-bookmark:before
	content: "\f36b"

.ion-android-bulb:before
	content: "\f36c"

.ion-android-bus:before
	content: "\f36d"

.ion-android-calendar:before
	content: "\f2d1"

.ion-android-call:before
	content: "\f2d2"

.ion-android-camera:before
	content: "\f2d3"

.ion-android-cancel:before
	content: "\f36e"

.ion-android-car:before
	content: "\f36f"

.ion-android-cart:before
	content: "\f370"

.ion-android-chat:before
	content: "\f2d4"

.ion-android-checkbox:before
	content: "\f374"

.ion-android-checkbox-blank:before
	content: "\f371"

.ion-android-checkbox-outline:before
	content: "\f373"

.ion-android-checkbox-outline-blank:before
	content: "\f372"

.ion-android-checkmark-circle:before
	content: "\f375"

.ion-android-clipboard:before
	content: "\f376"

.ion-android-close:before
	content: "\f2d7"

.ion-android-cloud:before
	content: "\f37a"

.ion-android-cloud-circle:before
	content: "\f377"

.ion-android-cloud-done:before
	content: "\f378"

.ion-android-cloud-outline:before
	content: "\f379"

.ion-android-color-palette:before
	content: "\f37b"

.ion-android-compass:before
	content: "\f37c"

.ion-android-contact:before
	content: "\f2d8"

.ion-android-contacts:before
	content: "\f2d9"

.ion-android-contract:before
	content: "\f37d"

.ion-android-create:before
	content: "\f37e"

.ion-android-delete:before
	content: "\f37f"

.ion-android-desktop:before
	content: "\f380"

.ion-android-document:before
	content: "\f381"

.ion-android-done:before
	content: "\f383"

.ion-android-done-all:before
	content: "\f382"

.ion-android-download:before
	content: "\f2dd"

.ion-android-drafts:before
	content: "\f384"

.ion-android-exit:before
	content: "\f385"

.ion-android-expand:before
	content: "\f386"

.ion-android-favorite:before
	content: "\f388"

.ion-android-favorite-outline:before
	content: "\f387"

.ion-android-film:before
	content: "\f389"

.ion-android-folder:before
	content: "\f2e0"

.ion-android-folder-open:before
	content: "\f38a"

.ion-android-funnel:before
	content: "\f38b"

.ion-android-globe:before
	content: "\f38c"

.ion-android-hand:before
	content: "\f2e3"

.ion-android-hangout:before
	content: "\f38d"

.ion-android-happy:before
	content: "\f38e"

.ion-android-home:before
	content: "\f38f"

.ion-android-image:before
	content: "\f2e4"

.ion-android-laptop:before
	content: "\f390"

.ion-android-list:before
	content: "\f391"

.ion-android-locate:before
	content: "\f2e9"

.ion-android-lock:before
	content: "\f392"

.ion-android-mail:before
	content: "\f2eb"

.ion-android-map:before
	content: "\f393"

.ion-android-menu:before
	content: "\f394"

.ion-android-microphone:before
	content: "\f2ec"

.ion-android-microphone-off:before
	content: "\f395"

.ion-android-more-horizontal:before
	content: "\f396"

.ion-android-more-vertical:before
	content: "\f397"

.ion-android-navigate:before
	content: "\f398"

.ion-android-notifications:before
	content: "\f39b"

.ion-android-notifications-none:before
	content: "\f399"

.ion-android-notifications-off:before
	content: "\f39a"

.ion-android-open:before
	content: "\f39c"

.ion-android-options:before
	content: "\f39d"

.ion-android-people:before
	content: "\f39e"

.ion-android-person:before
	content: "\f3a0"

.ion-android-person-add:before
	content: "\f39f"

.ion-android-phone-landscape:before
	content: "\f3a1"

.ion-android-phone-portrait:before
	content: "\f3a2"

.ion-android-pin:before
	content: "\f3a3"

.ion-android-plane:before
	content: "\f3a4"

.ion-android-playstore:before
	content: "\f2f0"

.ion-android-print:before
	content: "\f3a5"

.ion-android-radio-button-off:before
	content: "\f3a6"

.ion-android-radio-button-on:before
	content: "\f3a7"

.ion-android-refresh:before
	content: "\f3a8"

.ion-android-remove:before
	content: "\f2f4"

.ion-android-remove-circle:before
	content: "\f3a9"

.ion-android-restaurant:before
	content: "\f3aa"

.ion-android-sad:before
	content: "\f3ab"

.ion-android-search:before
	content: "\f2f5"

.ion-android-send:before
	content: "\f2f6"

.ion-android-settings:before
	content: "\f2f7"

.ion-android-share:before
	content: "\f2f8"

.ion-android-share-alt:before
	content: "\f3ac"

.ion-android-star:before
	content: "\f2fc"

.ion-android-star-half:before
	content: "\f3ad"

.ion-android-star-outline:before
	content: "\f3ae"

.ion-android-stopwatch:before
	content: "\f2fd"

.ion-android-subway:before
	content: "\f3af"

.ion-android-sunny:before
	content: "\f3b0"

.ion-android-sync:before
	content: "\f3b1"

.ion-android-textsms:before
	content: "\f3b2"

.ion-android-time:before
	content: "\f3b3"

.ion-android-train:before
	content: "\f3b4"

.ion-android-unlock:before
	content: "\f3b5"

.ion-android-upload:before
	content: "\f3b6"

.ion-android-volume-down:before
	content: "\f3b7"

.ion-android-volume-mute:before
	content: "\f3b8"

.ion-android-volume-off:before
	content: "\f3b9"

.ion-android-volume-up:before
	content: "\f3ba"

.ion-android-walk:before
	content: "\f3bb"

.ion-android-warning:before
	content: "\f3bc"

.ion-android-watch:before
	content: "\f3bd"

.ion-android-wifi:before
	content: "\f305"

.ion-aperture:before
	content: "\f313"

.ion-archive:before
	content: "\f102"

.ion-arrow-down-a:before
	content: "\f103"

.ion-arrow-down-b:before
	content: "\f104"

.ion-arrow-down-c:before
	content: "\f105"

.ion-arrow-expand:before
	content: "\f25e"

.ion-arrow-graph-down-left:before
	content: "\f25f"

.ion-arrow-graph-down-right:before
	content: "\f260"

.ion-arrow-graph-up-left:before
	content: "\f261"

.ion-arrow-graph-up-right:before
	content: "\f262"

.ion-arrow-left-a:before
	content: "\f106"

.ion-arrow-left-b:before
	content: "\f107"

.ion-arrow-left-c:before
	content: "\f108"

.ion-arrow-move:before
	content: "\f263"

.ion-arrow-resize:before
	content: "\f264"

.ion-arrow-return-left:before
	content: "\f265"

.ion-arrow-return-right:before
	content: "\f266"

.ion-arrow-right-a:before
	content: "\f109"

.ion-arrow-right-b:before
	content: "\f10a"

.ion-arrow-right-c:before
	content: "\f10b"

.ion-arrow-shrink:before
	content: "\f267"

.ion-arrow-swap:before
	content: "\f268"

.ion-arrow-up-a:before
	content: "\f10c"

.ion-arrow-up-b:before
	content: "\f10d"

.ion-arrow-up-c:before
	content: "\f10e"

.ion-asterisk:before
	content: "\f314"

.ion-at:before
	content: "\f10f"

.ion-backspace:before
	content: "\f3bf"

.ion-backspace-outline:before
	content: "\f3be"

.ion-bag:before
	content: "\f110"

.ion-battery-charging:before
	content: "\f111"

.ion-battery-empty:before
	content: "\f112"

.ion-battery-full:before
	content: "\f113"

.ion-battery-half:before
	content: "\f114"

.ion-battery-low:before
	content: "\f115"

.ion-beaker:before
	content: "\f269"

.ion-beer:before
	content: "\f26a"

.ion-bluetooth:before
	content: "\f116"

.ion-bonfire:before
	content: "\f315"

.ion-bookmark:before
	content: "\f26b"

.ion-bowtie:before
	content: "\f3c0"

.ion-briefcase:before
	content: "\f26c"

.ion-bug:before
	content: "\f2be"

.ion-calculator:before
	content: "\f26d"

.ion-calendar:before
	content: "\f117"

.ion-camera:before
	content: "\f118"

.ion-card:before
	content: "\f119"

.ion-cash:before
	content: "\f316"

.ion-chatbox:before
	content: "\f11b"

.ion-chatbox-working:before
	content: "\f11a"

.ion-chatboxes:before
	content: "\f11c"

.ion-chatbubble:before
	content: "\f11e"

.ion-chatbubble-working:before
	content: "\f11d"

.ion-chatbubbles:before
	content: "\f11f"

.ion-checkmark:before
	content: "\f122"

.ion-checkmark-circled:before
	content: "\f120"

.ion-checkmark-round:before
	content: "\f121"

.ion-chevron-down:before
	content: "\f123"

.ion-chevron-left:before
	content: "\f124"

.ion-chevron-right:before
	content: "\f125"

.ion-chevron-up:before
	content: "\f126"

.ion-clipboard:before
	content: "\f127"

.ion-clock:before
	content: "\f26e"

.ion-close:before
	content: "\f12a"

.ion-close-circled:before
	content: "\f128"

.ion-close-round:before
	content: "\f129"

.ion-closed-captioning:before
	content: "\f317"

.ion-cloud:before
	content: "\f12b"

.ion-code:before
	content: "\f271"

.ion-code-download:before
	content: "\f26f"

.ion-code-working:before
	content: "\f270"

.ion-coffee:before
	content: "\f272"

.ion-compass:before
	content: "\f273"

.ion-compose:before
	content: "\f12c"

.ion-connection-bars:before
	content: "\f274"

.ion-contrast:before
	content: "\f275"

.ion-crop:before
	content: "\f3c1"

.ion-cube:before
	content: "\f318"

.ion-disc:before
	content: "\f12d"

.ion-document:before
	content: "\f12f"

.ion-document-text:before
	content: "\f12e"

.ion-drag:before
	content: "\f130"

.ion-earth:before
	content: "\f276"

.ion-easel:before
	content: "\f3c2"

.ion-edit:before
	content: "\f2bf"

.ion-egg:before
	content: "\f277"

.ion-eject:before
	content: "\f131"

.ion-email:before
	content: "\f132"

.ion-email-unread:before
	content: "\f3c3"

.ion-erlenmeyer-flask:before
	content: "\f3c5"

.ion-erlenmeyer-flask-bubbles:before
	content: "\f3c4"

.ion-eye:before
	content: "\f133"

.ion-eye-disabled:before
	content: "\f306"

.ion-female:before
	content: "\f278"

.ion-filing:before
	content: "\f134"

.ion-film-marker:before
	content: "\f135"

.ion-fireball:before
	content: "\f319"

.ion-flag:before
	content: "\f279"

.ion-flame:before
	content: "\f31a"

.ion-flash:before
	content: "\f137"

.ion-flash-off:before
	content: "\f136"

.ion-folder:before
	content: "\f139"

.ion-fork:before
	content: "\f27a"

.ion-fork-repo:before
	content: "\f2c0"

.ion-forward:before
	content: "\f13a"

.ion-funnel:before
	content: "\f31b"

.ion-gear-a:before
	content: "\f13d"

.ion-gear-b:before
	content: "\f13e"

.ion-grid:before
	content: "\f13f"

.ion-hammer:before
	content: "\f27b"

.ion-happy:before
	content: "\f31c"

.ion-happy-outline:before
	content: "\f3c6"

.ion-headphone:before
	content: "\f140"

.ion-heart:before
	content: "\f141"

.ion-heart-broken:before
	content: "\f31d"

.ion-help:before
	content: "\f143"

.ion-help-buoy:before
	content: "\f27c"

.ion-help-circled:before
	content: "\f142"

.ion-home:before
	content: "\f144"

.ion-icecream:before
	content: "\f27d"

.ion-image:before
	content: "\f147"

.ion-images:before
	content: "\f148"

.ion-information:before
	content: "\f14a"

.ion-information-circled:before
	content: "\f149"

.ion-ionic:before
	content: "\f14b"

.ion-ios-alarm:before
	content: "\f3c8"

.ion-ios-alarm-outline:before
	content: "\f3c7"

.ion-ios-albums:before
	content: "\f3ca"

.ion-ios-albums-outline:before
	content: "\f3c9"

.ion-ios-americanfootball:before
	content: "\f3cc"

.ion-ios-americanfootball-outline:before
	content: "\f3cb"

.ion-ios-analytics:before
	content: "\f3ce"

.ion-ios-analytics-outline:before
	content: "\f3cd"

.ion-ios-arrow-back:before
	content: "\f3cf"

.ion-ios-arrow-down:before
	content: "\f3d0"

.ion-ios-arrow-forward:before
	content: "\f3d1"

.ion-ios-arrow-left:before
	content: "\f3d2"

.ion-ios-arrow-right:before
	content: "\f3d3"

.ion-ios-arrow-thin-down:before
	content: "\f3d4"

.ion-ios-arrow-thin-left:before
	content: "\f3d5"

.ion-ios-arrow-thin-right:before
	content: "\f3d6"

.ion-ios-arrow-thin-up:before
	content: "\f3d7"

.ion-ios-arrow-up:before
	content: "\f3d8"

.ion-ios-at:before
	content: "\f3da"

.ion-ios-at-outline:before
	content: "\f3d9"

.ion-ios-barcode:before
	content: "\f3dc"

.ion-ios-barcode-outline:before
	content: "\f3db"

.ion-ios-baseball:before
	content: "\f3de"

.ion-ios-baseball-outline:before
	content: "\f3dd"

.ion-ios-basketball:before
	content: "\f3e0"

.ion-ios-basketball-outline:before
	content: "\f3df"

.ion-ios-bell:before
	content: "\f3e2"

.ion-ios-bell-outline:before
	content: "\f3e1"

.ion-ios-body:before
	content: "\f3e4"

.ion-ios-body-outline:before
	content: "\f3e3"

.ion-ios-bolt:before
	content: "\f3e6"

.ion-ios-bolt-outline:before
	content: "\f3e5"

.ion-ios-book:before
	content: "\f3e8"

.ion-ios-book-outline:before
	content: "\f3e7"

.ion-ios-bookmarks:before
	content: "\f3ea"

.ion-ios-bookmarks-outline:before
	content: "\f3e9"

.ion-ios-box:before
	content: "\f3ec"

.ion-ios-box-outline:before
	content: "\f3eb"

.ion-ios-briefcase:before
	content: "\f3ee"

.ion-ios-briefcase-outline:before
	content: "\f3ed"

.ion-ios-browsers:before
	content: "\f3f0"

.ion-ios-browsers-outline:before
	content: "\f3ef"

.ion-ios-calculator:before
	content: "\f3f2"

.ion-ios-calculator-outline:before
	content: "\f3f1"

.ion-ios-calendar:before
	content: "\f3f4"

.ion-ios-calendar-outline:before
	content: "\f3f3"

.ion-ios-camera:before
	content: "\f3f6"

.ion-ios-camera-outline:before
	content: "\f3f5"

.ion-ios-cart:before
	content: "\f3f8"

.ion-ios-cart-outline:before
	content: "\f3f7"

.ion-ios-chatboxes:before
	content: "\f3fa"

.ion-ios-chatboxes-outline:before
	content: "\f3f9"

.ion-ios-chatbubble:before
	content: "\f3fc"

.ion-ios-chatbubble-outline:before
	content: "\f3fb"

.ion-ios-checkmark:before
	content: "\f3ff"

.ion-ios-checkmark-empty:before
	content: "\f3fd"

.ion-ios-checkmark-outline:before
	content: "\f3fe"

.ion-ios-circle-filled:before
	content: "\f400"

.ion-ios-circle-outline:before
	content: "\f401"

.ion-ios-clock:before
	content: "\f403"

.ion-ios-clock-outline:before
	content: "\f402"

.ion-ios-close:before
	content: "\f406"

.ion-ios-close-empty:before
	content: "\f404"

.ion-ios-close-outline:before
	content: "\f405"

.ion-ios-cloud:before
	content: "\f40c"

.ion-ios-cloud-download:before
	content: "\f408"

.ion-ios-cloud-download-outline:before
	content: "\f407"

.ion-ios-cloud-outline:before
	content: "\f409"

.ion-ios-cloud-upload:before
	content: "\f40b"

.ion-ios-cloud-upload-outline:before
	content: "\f40a"

.ion-ios-cloudy:before
	content: "\f410"

.ion-ios-cloudy-night:before
	content: "\f40e"

.ion-ios-cloudy-night-outline:before
	content: "\f40d"

.ion-ios-cloudy-outline:before
	content: "\f40f"

.ion-ios-cog:before
	content: "\f412"

.ion-ios-cog-outline:before
	content: "\f411"

.ion-ios-color-filter:before
	content: "\f414"

.ion-ios-color-filter-outline:before
	content: "\f413"

.ion-ios-color-wand:before
	content: "\f416"

.ion-ios-color-wand-outline:before
	content: "\f415"

.ion-ios-compose:before
	content: "\f418"

.ion-ios-compose-outline:before
	content: "\f417"

.ion-ios-contact:before
	content: "\f41a"

.ion-ios-contact-outline:before
	content: "\f419"

.ion-ios-copy:before
	content: "\f41c"

.ion-ios-copy-outline:before
	content: "\f41b"

.ion-ios-crop:before
	content: "\f41e"

.ion-ios-crop-strong:before
	content: "\f41d"

.ion-ios-download:before
	content: "\f420"

.ion-ios-download-outline:before
	content: "\f41f"

.ion-ios-drag:before
	content: "\f421"

.ion-ios-email:before
	content: "\f423"

.ion-ios-email-outline:before
	content: "\f422"

.ion-ios-eye:before
	content: "\f425"

.ion-ios-eye-outline:before
	content: "\f424"

.ion-ios-fastforward:before
	content: "\f427"

.ion-ios-fastforward-outline:before
	content: "\f426"

.ion-ios-filing:before
	content: "\f429"

.ion-ios-filing-outline:before
	content: "\f428"

.ion-ios-film:before
	content: "\f42b"

.ion-ios-film-outline:before
	content: "\f42a"

.ion-ios-flag:before
	content: "\f42d"

.ion-ios-flag-outline:before
	content: "\f42c"

.ion-ios-flame:before
	content: "\f42f"

.ion-ios-flame-outline:before
	content: "\f42e"

.ion-ios-flask:before
	content: "\f431"

.ion-ios-flask-outline:before
	content: "\f430"

.ion-ios-flower:before
	content: "\f433"

.ion-ios-flower-outline:before
	content: "\f432"

.ion-ios-folder:before
	content: "\f435"

.ion-ios-folder-outline:before
	content: "\f434"

.ion-ios-football:before
	content: "\f437"

.ion-ios-football-outline:before
	content: "\f436"

.ion-ios-game-controller-a:before
	content: "\f439"

.ion-ios-game-controller-a-outline:before
	content: "\f438"

.ion-ios-game-controller-b:before
	content: "\f43b"

.ion-ios-game-controller-b-outline:before
	content: "\f43a"

.ion-ios-gear:before
	content: "\f43d"

.ion-ios-gear-outline:before
	content: "\f43c"

.ion-ios-glasses:before
	content: "\f43f"

.ion-ios-glasses-outline:before
	content: "\f43e"

.ion-ios-grid-view:before
	content: "\f441"

.ion-ios-grid-view-outline:before
	content: "\f440"

.ion-ios-heart:before
	content: "\f443"

.ion-ios-heart-outline:before
	content: "\f442"

.ion-ios-help:before
	content: "\f446"

.ion-ios-help-empty:before
	content: "\f444"

.ion-ios-help-outline:before
	content: "\f445"

.ion-ios-home:before
	content: "\f448"

.ion-ios-home-outline:before
	content: "\f447"

.ion-ios-infinite:before
	content: "\f44a"

.ion-ios-infinite-outline:before
	content: "\f449"

.ion-ios-information:before
	content: "\f44d"

.ion-ios-information-empty:before
	content: "\f44b"

.ion-ios-information-outline:before
	content: "\f44c"

.ion-ios-ionic-outline:before
	content: "\f44e"

.ion-ios-keypad:before
	content: "\f450"

.ion-ios-keypad-outline:before
	content: "\f44f"

.ion-ios-lightbulb:before
	content: "\f452"

.ion-ios-lightbulb-outline:before
	content: "\f451"

.ion-ios-list:before
	content: "\f454"

.ion-ios-list-outline:before
	content: "\f453"

.ion-ios-location:before
	content: "\f456"

.ion-ios-location-outline:before
	content: "\f455"

.ion-ios-locked:before
	content: "\f458"

.ion-ios-locked-outline:before
	content: "\f457"

.ion-ios-loop:before
	content: "\f45a"

.ion-ios-loop-strong:before
	content: "\f459"

.ion-ios-medical:before
	content: "\f45c"

.ion-ios-medical-outline:before
	content: "\f45b"

.ion-ios-medkit:before
	content: "\f45e"

.ion-ios-medkit-outline:before
	content: "\f45d"

.ion-ios-mic:before
	content: "\f461"

.ion-ios-mic-off:before
	content: "\f45f"

.ion-ios-mic-outline:before
	content: "\f460"

.ion-ios-minus:before
	content: "\f464"

.ion-ios-minus-empty:before
	content: "\f462"

.ion-ios-minus-outline:before
	content: "\f463"

.ion-ios-monitor:before
	content: "\f466"

.ion-ios-monitor-outline:before
	content: "\f465"

.ion-ios-moon:before
	content: "\f468"

.ion-ios-moon-outline:before
	content: "\f467"

.ion-ios-more:before
	content: "\f46a"

.ion-ios-more-outline:before
	content: "\f469"

.ion-ios-musical-note:before
	content: "\f46b"

.ion-ios-musical-notes:before
	content: "\f46c"

.ion-ios-navigate:before
	content: "\f46e"

.ion-ios-navigate-outline:before
	content: "\f46d"

.ion-ios-nutrition:before
	content: "\f470"

.ion-ios-nutrition-outline:before
	content: "\f46f"

.ion-ios-paper:before
	content: "\f472"

.ion-ios-paper-outline:before
	content: "\f471"

.ion-ios-paperplane:before
	content: "\f474"

.ion-ios-paperplane-outline:before
	content: "\f473"

.ion-ios-partlysunny:before
	content: "\f476"

.ion-ios-partlysunny-outline:before
	content: "\f475"

.ion-ios-pause:before
	content: "\f478"

.ion-ios-pause-outline:before
	content: "\f477"

.ion-ios-paw:before
	content: "\f47a"

.ion-ios-paw-outline:before
	content: "\f479"

.ion-ios-people:before
	content: "\f47c"

.ion-ios-people-outline:before
	content: "\f47b"

.ion-ios-person:before
	content: "\f47e"

.ion-ios-person-outline:before
	content: "\f47d"

.ion-ios-personadd:before
	content: "\f480"

.ion-ios-personadd-outline:before
	content: "\f47f"

.ion-ios-photos:before
	content: "\f482"

.ion-ios-photos-outline:before
	content: "\f481"

.ion-ios-pie:before
	content: "\f484"

.ion-ios-pie-outline:before
	content: "\f483"

.ion-ios-pint:before
	content: "\f486"

.ion-ios-pint-outline:before
	content: "\f485"

.ion-ios-play:before
	content: "\f488"

.ion-ios-play-outline:before
	content: "\f487"

.ion-ios-plus:before
	content: "\f48b"

.ion-ios-plus-empty:before
	content: "\f489"

.ion-ios-plus-outline:before
	content: "\f48a"

.ion-ios-pricetag:before
	content: "\f48d"

.ion-ios-pricetag-outline:before
	content: "\f48c"

.ion-ios-pricetags:before
	content: "\f48f"

.ion-ios-pricetags-outline:before
	content: "\f48e"

.ion-ios-printer:before
	content: "\f491"

.ion-ios-printer-outline:before
	content: "\f490"

.ion-ios-pulse:before
	content: "\f493"

.ion-ios-pulse-strong:before
	content: "\f492"

.ion-ios-rainy:before
	content: "\f495"

.ion-ios-rainy-outline:before
	content: "\f494"

.ion-ios-recording:before
	content: "\f497"

.ion-ios-recording-outline:before
	content: "\f496"

.ion-ios-redo:before
	content: "\f499"

.ion-ios-redo-outline:before
	content: "\f498"

.ion-ios-refresh:before
	content: "\f49c"

.ion-ios-refresh-empty:before
	content: "\f49a"

.ion-ios-refresh-outline:before
	content: "\f49b"

.ion-ios-reload:before
	content: "\f49d"

.ion-ios-reverse-camera:before
	content: "\f49f"

.ion-ios-reverse-camera-outline:before
	content: "\f49e"

.ion-ios-rewind:before
	content: "\f4a1"

.ion-ios-rewind-outline:before
	content: "\f4a0"

.ion-ios-rose:before
	content: "\f4a3"

.ion-ios-rose-outline:before
	content: "\f4a2"

.ion-ios-search:before
	content: "\f4a5"

.ion-ios-search-strong:before
	content: "\f4a4"

.ion-ios-settings:before
	content: "\f4a7"

.ion-ios-settings-strong:before
	content: "\f4a6"

.ion-ios-shuffle:before
	content: "\f4a9"

.ion-ios-shuffle-strong:before
	content: "\f4a8"

.ion-ios-skipbackward:before
	content: "\f4ab"

.ion-ios-skipbackward-outline:before
	content: "\f4aa"

.ion-ios-skipforward:before
	content: "\f4ad"

.ion-ios-skipforward-outline:before
	content: "\f4ac"

.ion-ios-snowy:before
	content: "\f4ae"

.ion-ios-speedometer:before
	content: "\f4b0"

.ion-ios-speedometer-outline:before
	content: "\f4af"

.ion-ios-star:before
	content: "\f4b3"

.ion-ios-star-half:before
	content: "\f4b1"

.ion-ios-star-outline:before
	content: "\f4b2"

.ion-ios-stopwatch:before
	content: "\f4b5"

.ion-ios-stopwatch-outline:before
	content: "\f4b4"

.ion-ios-sunny:before
	content: "\f4b7"

.ion-ios-sunny-outline:before
	content: "\f4b6"

.ion-ios-telephone:before
	content: "\f4b9"

.ion-ios-telephone-outline:before
	content: "\f4b8"

.ion-ios-tennisball:before
	content: "\f4bb"

.ion-ios-tennisball-outline:before
	content: "\f4ba"

.ion-ios-thunderstorm:before
	content: "\f4bd"

.ion-ios-thunderstorm-outline:before
	content: "\f4bc"

.ion-ios-time:before
	content: "\f4bf"

.ion-ios-time-outline:before
	content: "\f4be"

.ion-ios-timer:before
	content: "\f4c1"

.ion-ios-timer-outline:before
	content: "\f4c0"

.ion-ios-toggle:before
	content: "\f4c3"

.ion-ios-toggle-outline:before
	content: "\f4c2"

.ion-ios-trash:before
	content: "\f4c5"

.ion-ios-trash-outline:before
	content: "\f4c4"

.ion-ios-undo:before
	content: "\f4c7"

.ion-ios-undo-outline:before
	content: "\f4c6"

.ion-ios-unlocked:before
	content: "\f4c9"

.ion-ios-unlocked-outline:before
	content: "\f4c8"

.ion-ios-upload:before
	content: "\f4cb"

.ion-ios-upload-outline:before
	content: "\f4ca"

.ion-ios-videocam:before
	content: "\f4cd"

.ion-ios-videocam-outline:before
	content: "\f4cc"

.ion-ios-volume-high:before
	content: "\f4ce"

.ion-ios-volume-low:before
	content: "\f4cf"

.ion-ios-wineglass:before
	content: "\f4d1"

.ion-ios-wineglass-outline:before
	content: "\f4d0"

.ion-ios-world:before
	content: "\f4d3"

.ion-ios-world-outline:before
	content: "\f4d2"

.ion-ipad:before
	content: "\f1f9"

.ion-iphone:before
	content: "\f1fa"

.ion-ipod:before
	content: "\f1fb"

.ion-jet:before
	content: "\f295"

.ion-key:before
	content: "\f296"

.ion-knife:before
	content: "\f297"

.ion-laptop:before
	content: "\f1fc"

.ion-leaf:before
	content: "\f1fd"

.ion-levels:before
	content: "\f298"

.ion-lightbulb:before
	content: "\f299"

.ion-link:before
	content: "\f1fe"

.ion-load-a:before
	content: "\f29a"

.ion-load-b:before
	content: "\f29b"

.ion-load-c:before
	content: "\f29c"

.ion-load-d:before
	content: "\f29d"

.ion-location:before
	content: "\f1ff"

.ion-lock-combination:before
	content: "\f4d4"

.ion-locked:before
	content: "\f200"

.ion-log-in:before
	content: "\f29e"

.ion-log-out:before
	content: "\f29f"

.ion-loop:before
	content: "\f201"

.ion-magnet:before
	content: "\f2a0"

.ion-male:before
	content: "\f2a1"

.ion-man:before
	content: "\f202"

.ion-map:before
	content: "\f203"

.ion-medkit:before
	content: "\f2a2"

.ion-merge:before
	content: "\f33f"

.ion-mic-a:before
	content: "\f204"

.ion-mic-b:before
	content: "\f205"

.ion-mic-c:before
	content: "\f206"

.ion-minus:before
	content: "\f209"

.ion-minus-circled:before
	content: "\f207"

.ion-minus-round:before
	content: "\f208"

.ion-model-s:before
	content: "\f2c1"

.ion-monitor:before
	content: "\f20a"

.ion-more:before
	content: "\f20b"

.ion-mouse:before
	content: "\f340"

.ion-music-note:before
	content: "\f20c"

.ion-navicon:before
	content: "\f20e"

.ion-navicon-round:before
	content: "\f20d"

.ion-navigate:before
	content: "\f2a3"

.ion-network:before
	content: "\f341"

.ion-no-smoking:before
	content: "\f2c2"

.ion-nuclear:before
	content: "\f2a4"

.ion-outlet:before
	content: "\f342"

.ion-paintbrush:before
	content: "\f4d5"

.ion-paintbucket:before
	content: "\f4d6"

.ion-paper-airplane:before
	content: "\f2c3"

.ion-paperclip:before
	content: "\f20f"

.ion-pause:before
	content: "\f210"

.ion-person:before
	content: "\f213"

.ion-person-add:before
	content: "\f211"

.ion-person-stalker:before
	content: "\f212"

.ion-pie-graph:before
	content: "\f2a5"

.ion-pin:before
	content: "\f2a6"

.ion-pinpoint:before
	content: "\f2a7"

.ion-pizza:before
	content: "\f2a8"

.ion-plane:before
	content: "\f214"

.ion-planet:before
	content: "\f343"

.ion-play:before
	content: "\f215"

.ion-playstation:before
	content: "\f30a"

.ion-plus:before
	content: "\f218"

.ion-plus-circled:before
	content: "\f216"

.ion-plus-round:before
	content: "\f217"

.ion-podium:before
	content: "\f344"

.ion-pound:before
	content: "\f219"

.ion-power:before
	content: "\f2a9"

.ion-pricetag:before
	content: "\f2aa"

.ion-pricetags:before
	content: "\f2ab"

.ion-printer:before
	content: "\f21a"

.ion-pull-request:before
	content: "\f345"

.ion-qr-scanner:before
	content: "\f346"

.ion-quote:before
	content: "\f347"

.ion-radio-waves:before
	content: "\f2ac"

.ion-record:before
	content: "\f21b"

.ion-refresh:before
	content: "\f21c"

.ion-reply:before
	content: "\f21e"

.ion-reply-all:before
	content: "\f21d"

.ion-ribbon-a:before
	content: "\f348"

.ion-ribbon-b:before
	content: "\f349"

.ion-sad:before
	content: "\f34a"

.ion-sad-outline:before
	content: "\f4d7"

.ion-scissors:before
	content: "\f34b"

.ion-search:before
	content: "\f21f"

.ion-settings:before
	content: "\f2ad"

.ion-share:before
	content: "\f220"

.ion-shuffle:before
	content: "\f221"

.ion-skip-backward:before
	content: "\f222"

.ion-skip-forward:before
	content: "\f223"

.ion-social-android:before
	content: "\f225"

.ion-social-android-outline:before
	content: "\f224"

.ion-social-angular:before
	content: "\f4d9"

.ion-social-angular-outline:before
	content: "\f4d8"

.ion-social-apple:before
	content: "\f227"

.ion-social-apple-outline:before
	content: "\f226"

.ion-social-bitcoin:before
	content: "\f2af"

.ion-social-bitcoin-outline:before
	content: "\f2ae"

.ion-social-buffer:before
	content: "\f229"

.ion-social-buffer-outline:before
	content: "\f228"

.ion-social-chrome:before
	content: "\f4db"

.ion-social-chrome-outline:before
	content: "\f4da"

.ion-social-codepen:before
	content: "\f4dd"

.ion-social-codepen-outline:before
	content: "\f4dc"

.ion-social-css3:before
	content: "\f4df"

.ion-social-css3-outline:before
	content: "\f4de"

.ion-social-designernews:before
	content: "\f22b"

.ion-social-designernews-outline:before
	content: "\f22a"

.ion-social-dribbble:before
	content: "\f22d"

.ion-social-dribbble-outline:before
	content: "\f22c"

.ion-social-dropbox:before
	content: "\f22f"

.ion-social-dropbox-outline:before
	content: "\f22e"

.ion-social-euro:before
	content: "\f4e1"

.ion-social-euro-outline:before
	content: "\f4e0"

.ion-social-facebook:before
	content: "\f231"

.ion-social-facebook-outline:before
	content: "\f230"

.ion-social-foursquare:before
	content: "\f34d"

.ion-social-foursquare-outline:before
	content: "\f34c"

.ion-social-freebsd-devil:before
	content: "\f2c4"

.ion-social-github:before
	content: "\f233"

.ion-social-github-outline:before
	content: "\f232"

.ion-social-google:before
	content: "\f34f"

.ion-social-google-outline:before
	content: "\f34e"

.ion-social-googleplus:before
	content: "\f235"

.ion-social-googleplus-outline:before
	content: "\f234"

.ion-social-hackernews:before
	content: "\f237"

.ion-social-hackernews-outline:before
	content: "\f236"

.ion-social-html5:before
	content: "\f4e3"

.ion-social-html5-outline:before
	content: "\f4e2"

.ion-social-instagram:before
	content: "\f351"

.ion-social-instagram-outline:before
	content: "\f350"

.ion-social-javascript:before
	content: "\f4e5"

.ion-social-javascript-outline:before
	content: "\f4e4"

.ion-social-linkedin:before
	content: "\f239"

.ion-social-linkedin-outline:before
	content: "\f238"

.ion-social-markdown:before
	content: "\f4e6"

.ion-social-nodejs:before
	content: "\f4e7"

.ion-social-octocat:before
	content: "\f4e8"

.ion-social-pinterest:before
	content: "\f2b1"

.ion-social-pinterest-outline:before
	content: "\f2b0"

.ion-social-python:before
	content: "\f4e9"

.ion-social-reddit:before
	content: "\f23b"

.ion-social-reddit-outline:before
	content: "\f23a"

.ion-social-rss:before
	content: "\f23d"

.ion-social-rss-outline:before
	content: "\f23c"

.ion-social-sass:before
	content: "\f4ea"

.ion-social-skype:before
	content: "\f23f"

.ion-social-skype-outline:before
	content: "\f23e"

.ion-social-snapchat:before
	content: "\f4ec"

.ion-social-snapchat-outline:before
	content: "\f4eb"

.ion-social-tumblr:before
	content: "\f241"

.ion-social-tumblr-outline:before
	content: "\f240"

.ion-social-tux:before
	content: "\f2c5"

.ion-social-twitch:before
	content: "\f4ee"

.ion-social-twitch-outline:before
	content: "\f4ed"

.ion-social-twitter:before
	content: "\f243"

.ion-social-twitter-outline:before
	content: "\f242"

.ion-social-usd:before
	content: "\f353"

.ion-social-usd-outline:before
	content: "\f352"

.ion-social-vimeo:before
	content: "\f245"

.ion-social-vimeo-outline:before
	content: "\f244"

.ion-social-whatsapp:before
	content: "\f4f0"

.ion-social-whatsapp-outline:before
	content: "\f4ef"

.ion-social-windows:before
	content: "\f247"

.ion-social-windows-outline:before
	content: "\f246"

.ion-social-wordpress:before
	content: "\f249"

.ion-social-wordpress-outline:before
	content: "\f248"

.ion-social-yahoo:before
	content: "\f24b"

.ion-social-yahoo-outline:before
	content: "\f24a"

.ion-social-yen:before
	content: "\f4f2"

.ion-social-yen-outline:before
	content: "\f4f1"

.ion-social-youtube:before
	content: "\f24d"

.ion-social-youtube-outline:before
	content: "\f24c"

.ion-soup-can:before
	content: "\f4f4"

.ion-soup-can-outline:before
	content: "\f4f3"

.ion-speakerphone:before
	content: "\f2b2"

.ion-speedometer:before
	content: "\f2b3"

.ion-spoon:before
	content: "\f2b4"

.ion-star:before
	content: "\f24e"

.ion-stats-bars:before
	content: "\f2b5"

.ion-steam:before
	content: "\f30b"

.ion-stop:before
	content: "\f24f"

.ion-thermometer:before
	content: "\f2b6"

.ion-thumbsdown:before
	content: "\f250"

.ion-thumbsup:before
	content: "\f251"

.ion-toggle:before
	content: "\f355"

.ion-toggle-filled:before
	content: "\f354"

.ion-transgender:before
	content: "\f4f5"

.ion-trash-a:before
	content: "\f252"

.ion-trash-b:before
	content: "\f253"

.ion-trophy:before
	content: "\f356"

.ion-tshirt:before
	content: "\f4f7"

.ion-tshirt-outline:before
	content: "\f4f6"

.ion-umbrella:before
	content: "\f2b7"

.ion-university:before
	content: "\f357"

.ion-unlocked:before
	content: "\f254"

.ion-upload:before
	content: "\f255"

.ion-usb:before
	content: "\f2b8"

.ion-videocamera:before
	content: "\f256"

.ion-volume-high:before
	content: "\f257"

.ion-volume-low:before
	content: "\f258"

.ion-volume-medium:before
	content: "\f259"

.ion-volume-mute:before
	content: "\f25a"

.ion-wand:before
	content: "\f358"

.ion-waterdrop:before
	content: "\f25b"

.ion-wifi:before
	content: "\f25c"

.ion-wineglass:before
	content: "\f2b9"

.ion-woman:before
	content: "\f25d"

.ion-wrench:before
	content: "\f2ba"

.ion-xbox:before
	content: "\f30c"

.step__item.active
	.step__icon
		background-color: $color_12

	~
		&.step__divider
			background-color: $color_12

		&.step__item .step__icon
			background-color: $color_12