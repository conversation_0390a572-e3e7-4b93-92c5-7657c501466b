<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * 运行权限初始化
     * @return void
     */
    public function run()
    {
        // 用户组相关细粒度权限
        $permissions = [
            'manage user group', // 用户组管理总控
            'create user group', // 创建用户组
            'edit user group',   // 编辑用户组
            'delete user group', // 删除用户组
            'show user group',   // 显示用户组
            // 如需成员管理权限，可继续补充
            'manage group member',
            'add group member',
            'remove group member',
            'show group member',
        ];
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }
        // manager默认分配全部用户组相关权限
        $managerRole = Role::whereIn('name', ['manager', 'owner'])->first();
        if ($managerRole) {
            $managerRole->givePermissionTo($permissions);
        }
    }
} 