<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApplicantCommentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(
            'applicant_comments', function (Blueprint $table){
            $table->id();
            $table->integer('applicant')->default(0);
            $table->integer('commented_by')->default(0);
            $table->text('comment')->nullable();
            $table->integer('parent_id')->default(0);
            $table->timestamps();
        }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicant_comments');
    }
}
