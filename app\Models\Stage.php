<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stage extends Model
{
    use HasFactory;
    protected $fillable = [
        'stage',
        'order',
        'parent_id',
    ];

    public function applicants()
    {
        $query = Applicant::where('stage', $this->id)->where('is_archive', 0);
        
        // 如果传递了限制的职位ID列表，则使用它进行过滤
        if (request()->has('assignedJobIds')) {
            $assignedJobIds = request('assignedJobIds');
            $query->whereIn('job', $assignedJobIds);
        }
        
        // 如果当前用户没有全局权限，且session中有assignedJobIds，也进行过滤
        else if (session()->has('assignedJobIds') && !\Auth::user()->can('manage applicant')) {
            $assignedJobIds = session('assignedJobIds');
            $query->whereIn('job', $assignedJobIds);
        }
        
        return $query->orderBy('order')->get();
    }
}
