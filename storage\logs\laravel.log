[2025-07-22 12:48:52] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'value' cannot be null (SQL: insert into settings (`value`, `name`,`type`,`parent_id`) values (?, pricing_feature, common,1) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'value' cannot be null (SQL: insert into settings (`value`, `name`,`type`,`parent_id`) values (?, pricing_feature, common,1) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ) at D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('insert into set...', Array, Object(Closure))
#1 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(534): Illuminate\\Database\\Connection->run('insert into set...', Array, Object(Closure))
#2 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement('insert into set...', Array)
#3 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(469): Illuminate\\Database\\Connection->insert('insert into set...', Array)
#4 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\DatabaseManager->__call('insert', Array)
#5 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Controllers\\SettingController.php(236): Illuminate\\Support\\Facades\\Facade::__callStatic('insert', Array)
#6 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\SettingController->generalData(Object(Illuminate\\Http\\Request))
#7 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('generalData', Array)
#8 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\SettingController), 'generalData')
#9 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#11 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Middleware\\XSS.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\XSS->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Middleware\\Verify2FA.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\Verify2FA->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\www\\bingo_space\\zhaoping\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'value' cannot be null at D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into set...', Array)
#2 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('insert into set...', Array, Object(Closure))
#3 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(534): Illuminate\\Database\\Connection->run('insert into set...', Array, Object(Closure))
#4 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement('insert into set...', Array)
#5 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(469): Illuminate\\Database\\Connection->insert('insert into set...', Array)
#6 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\DatabaseManager->__call('insert', Array)
#7 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Controllers\\SettingController.php(236): Illuminate\\Support\\Facades\\Facade::__callStatic('insert', Array)
#8 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\SettingController->generalData(Object(Illuminate\\Http\\Request))
#9 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('generalData', Array)
#10 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\SettingController), 'generalData')
#11 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#13 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Middleware\\XSS.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\XSS->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\www\\bingo_space\\zhaoping\\app\\Http\\Middleware\\Verify2FA.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\Verify2FA->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(797): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\www\\bingo_space\\zhaoping\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\www\\bingo_space\\zhaoping\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-30 02:27:14] local.INFO: Dashboard Recent Interviews Debug {"user_id":5,"user_type":"hod","user_roles":{"Illuminate\\Support\\Collection":["hod"]},"parent_id_value":1,"user_parent_id":1} 
[2025-07-30 02:27:16] local.INFO: Dashboard Recent Interviews Data {"interviews_count":3,"interviews_data":{"Illuminate\\Support\\Collection":[{"id":7,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"id":6,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"id":5,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"}]}} 
[2025-07-30 02:28:56] local.INFO: Dashboard Recent Interviews Debug {"user_id":5,"user_type":"hod","user_roles":{"Illuminate\\Support\\Collection":["hod"]},"parent_id_value":1,"user_parent_id":1} 
[2025-07-30 02:28:56] local.INFO: Dashboard Recent Interviews Data {"interviews_count":3,"interviews_data":{"Illuminate\\Support\\Collection":[{"id":7,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"id":6,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"id":5,"parent_id":1,"applicant_id":2,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"}]}} 
[2025-07-30 02:31:29] local.INFO: Dashboard Recent Interviews Debug {"user_id":5,"user_type":"hod","user_roles":{"Illuminate\\Support\\Collection":["hod"]},"parent_id_value":1,"user_parent_id":1} 
[2025-07-30 02:31:30] local.INFO: Dashboard Recent Interviews Data {"interviews_count":3,"interviews_data":{"Illuminate\\Support\\Collection":[{"interview_id":7,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":6,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":5,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"}]}} 
[2025-07-30 02:32:01] local.INFO: Dashboard Recent Interviews Debug {"user_id":5,"user_type":"hod","user_roles":{"Illuminate\\Support\\Collection":["hod"]},"parent_id_value":1,"user_parent_id":1} 
[2025-07-30 02:32:06] local.INFO: Dashboard Recent Interviews Data {"interviews_count":3,"interviews_data":{"Illuminate\\Support\\Collection":[{"interview_id":7,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":6,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":5,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"}]}} 
[2025-07-30 02:33:59] local.INFO: Dashboard Recent Interviews Debug {"user_id":5,"user_type":"hod","user_roles":{"Illuminate\\Support\\Collection":["hod"]},"parent_id_value":1,"user_parent_id":1} 
[2025-07-30 02:34:05] local.INFO: Dashboard Recent Interviews Data {"interviews_count":3,"interviews_data":{"Illuminate\\Support\\Collection":[{"interview_id":7,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":6,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"},{"interview_id":5,"interview_parent_id":1,"applicant_id":2,"applicant_exists":false,"applicant_in_scope":false,"applicant_parent_id":null,"applicant_name":null,"applicant_email":null,"assign_user":5,"user_name":"关羽"}]}} 
