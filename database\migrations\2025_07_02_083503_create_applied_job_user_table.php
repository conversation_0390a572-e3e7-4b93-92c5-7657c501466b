<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applied_job_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('applied_job_id');
            $table->unsignedBigInteger('user_id');
            $table->string('source_type')->nullable()->comment('关联来源类型: direct-直接用户, group-用户组');
            $table->unsignedBigInteger('source_id')->nullable()->comment('如果来自用户组，存储用户组ID');
            $table->timestamps();
            
            $table->index('applied_job_id');
            $table->index('user_id');
            $table->index('source_type');
            $table->index('source_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applied_job_user');
    }
};
