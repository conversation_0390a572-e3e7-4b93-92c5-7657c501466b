<?php

namespace App\Services;

use App\Models\UserGroup;
use App\Models\UserGroupMember;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UserGroupService
{
    /**
     * 创建用户组
     * @param array $data
     * @return UserGroup
     */
    public function createGroup(array $data)
    {
        // 创建用户组
        return UserGroup::create($data);
    }

    /**
     * 更新用户组
     * @param UserGroup $group
     * @param array $data
     * @return bool
     */
    public function updateGroup(UserGroup $group, array $data)
    {
        return $group->update($data);
    }

    /**
     * 软删除用户组
     * @param UserGroup $group
     * @return bool|null
     */
    public function deleteGroup(UserGroup $group)
    {
        return $group->delete();
    }

    /**
     * 恢复软删除的用户组
     * @param int $id
     * @return bool
     */
    public function restoreGroup($id)
    {
        $group = UserGroup::withTrashed()->find($id);
        if ($group) {
            return $group->restore();
        }
        return false;
    }

    /**
     * 添加成员到用户组
     * @param int $groupId
     * @param int $userId
     * @return UserGroupMember
     */
    public function addMember($groupId, $userId)
    {
        // 避免重复添加
        $member = UserGroupMember::withTrashed()->where('group_id', $groupId)->where('user_id', $userId)->first();
        if ($member) {
            if ($member->trashed()) {
                $member->restore();
            }
            return $member;
        }
        return UserGroupMember::create([
            'group_id' => $groupId,
            'user_id' => $userId,
        ]);
    }

    /**
     * 从用户组移除成员（软删除）
     * @param int $groupId
     * @param int $userId
     * @return bool|null
     */
    public function removeMember($groupId, $userId)
    {
        $member = UserGroupMember::where('group_id', $groupId)->where('user_id', $userId)->first();
        if ($member) {
            return $member->delete();
        }
        return false;
    }

    /**
     * 获取用户组成员列表
     * @param int $groupId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getGroupMembers($groupId)
    {
        return UserGroupMember::where('group_id', $groupId)->whereNull('deleted_at')->with('user')->get();
    }

    /**
     * 获取用户所有分组
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserGroups($userId)
    {
        return UserGroup::whereHas('users', function($query) use ($userId) {
            $query->where('users.id', $userId);
        })->get();
    }
} 