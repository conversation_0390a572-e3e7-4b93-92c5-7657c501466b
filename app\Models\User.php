<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Lab404\Impersonate\Models\Impersonate;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    use Notifiable;
    use Impersonate;


    /**
     * Google 账号唯一ID，用于第三方登录绑定
     * @var string|null
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'type',
        'phone_number',
        'profile',
        'lang',

        'parent_id',
        'code',
        'is_active',
        'twofa_secret',
        'google_id', // Google 账号唯一ID
        'login_failed_attempts', // 登录失败次数
        'locked_at', // 账户锁定时间
        'lock_reason', // 锁定原因
    ];


    protected $hidden = [
        'password',
        'remember_token',
    ];


    protected $casts = [
        'email_verified_at' => 'datetime',
    ];



    public function totalUser()
    {
        return User::whereNotIn('type', ['tenant', 'maintainer'])->where('parent_id', $this->id)->count();
    }
    public function totalTenant()
    {
        return User::where('type', 'tenant')->where('parent_id', $this->id)->count();
    }

    public function totalContact()
    {
        return Contact::where('parent_id', '=', parentId())->count();
    }

    public function roleWiseUserCount($role)
    {
        return User::where('type', $role)->where('parent_id', parentId())->count();
    }
    public function totalJob()
    {
        return AppliedJob::where('parent_id', parentId())->count();
    }
    public static function getDevice($user)
    {
        $mobileType = '/(?:phone|windows\s+phone|ipod|blackberry|(?:android|bb\d+|meego|silk|googlebot) .+? mobile|palm|windows\s+ce|opera mini|avantgo|mobilesafari|docomo)/i';
        $tabletType = '/(?:ipad|playbook|(?:android|bb\d+|meego|silk)(?! .+? mobile))/i';
        if (preg_match_all($mobileType, $user)) {
            return 'mobile';
        } else {
            if (preg_match_all($tabletType, $user)) {
                return 'tablet';
            } else {
                return 'desktop';
            }
        }
    }



    public static $systemModules = [
        'user group',      // 用户组管理模块
        'group member',    // 用户组成员管理模块
        'user',            // 用户管理模块
        'applied job',
        'applicant',
        'interview',
        'employee',
        'category',
        'location',
        'stage',
        'skill',
        'question',
        'type',
        'contact',
        'note',
        'logged history',
        'pricing transation',
        'account settings',
        'password settings',
        'general settings',
        'company settings',
    ];

    /**
     * 系统允许的角色
     */
    public static $allowedRoles = [
        'owner' => '管理员',
        'manager' => '经理', 
        'hod' => '部门主管'
    ];

    /**
     * 系统预设角色（不可编辑/删除）
     */
    public static $systemRoles = ['owner', 'manager'];

    /**
     * 可创建的用户角色（排除owner）
     */
    public static $creatableRoles = ['manager', 'hod'];

    /**
     * 检查账户是否被锁定
     */
    public function isLocked()
    {
        // 管理员和经理不会被锁定
        if ($this->hasRole(['owner', 'manager'])) {
            return false;
        }
        
        return !is_null($this->locked_at);
    }

    /**
     * 锁定账户
     */
    public function lockAccount($reason = '登录失败次数过多')
    {
        // 管理员和经理不能被锁定
        if ($this->hasRole(['owner', 'manager'])) {
            return;
        }
        
        $this->update([
            'locked_at' => now(),
            'lock_reason' => $reason,
        ]);
    }

    /**
     * 解锁账户
     */
    public function unlockAccount()
    {
        $this->update([
            'locked_at' => null,
            'lock_reason' => null,
            'login_failed_attempts' => 0,
        ]);
    }

    /**
     * 增加登录失败次数
     */
    public function incrementFailedAttempts()
    {
        // 管理员和经理不受登录失败次数限制
        if ($this->hasRole(['owner', 'manager'])) {
            return;
        }
        
        $this->increment('login_failed_attempts');
        
        // 如果失败次数达到5次，锁定账户
        if ($this->login_failed_attempts >= 5) {
            $this->lockAccount('登录失败次数超过5次');
        }
    }

    /**
     * 重置登录失败次数
     */
    public function resetFailedAttempts()
    {
        $this->update(['login_failed_attempts' => 0]);
    }

    /**
     * 检查是否有解锁权限
     */
    public function canUnlockAccounts()
    {
        return $this->hasRole(['owner', 'manager']);
    }

    /**
     * 用户所属的所有用户组（多对多）
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function userGroups()
    {
        // 通过user_group_members表建立多对多关系
        return $this->belongsToMany(UserGroup::class, 'user_group_members', 'user_id', 'group_id')
            ->withTimestamps()
            ->withPivot('deleted_at')
            ->wherePivotNull('deleted_at'); // 只获取未软删除的分组
    }

    /**
     * 用户作为成员的所有用户组成员记录（一对多）
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userGroupMembers()
    {
        return $this->hasMany(UserGroupMember::class, 'user_id', 'id');
    }

    /**
     * 获取用户有权限处理的职位
     */
    public function assignedJobs()
    {
        return $this->belongsToMany(AppliedJob::class, 'applied_job_user')
                    ->withPivot('source_type', 'source_id')
                    ->withTimestamps();
    }
}
