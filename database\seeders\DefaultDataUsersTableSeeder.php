<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Route;

class DefaultDataUsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $currentRouteName = Route::currentRouteName();
        if ($currentRouteName != 'LaravelUpdater::database') {

            // Default All Permission
            $allPermission = [
                [
                    'name' => 'manage user',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create user',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit user',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete user',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'show user',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage role',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create role',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit role',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete role',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage contact',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create contact',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit contact',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete contact',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage note',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create note',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit note',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete note',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage logged history',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete logged history',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage pricing packages',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create pricing packages',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit pricing packages',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete pricing packages',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'buy pricing packages',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage pricing transation',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage coupon',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create coupon',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit coupon',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete coupon',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage coupon history',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete coupon history',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage account settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage password settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage general settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage company settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage email settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage payment settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage seo settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage google recaptcha settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage notification',
                    'gaurd_name' => 'web',
                ],
                [
                    'name' => 'edit notification',
                    'gaurd_name' => 'web',
                ],
                [
                    'name' => 'manage FAQ',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create FAQ',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit FAQ',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete FAQ',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage Page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'create Page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit Page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'delete Page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'show Page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage home page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit home page',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage footer',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'edit footer',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage 2FA settings',
                    'guard_name' => 'web',
                ],
                [
                    'name' => 'manage auth page',
                    'guard_name' => 'web',
                ],

                [
                    'name' => "manage applied job",
                    'guard_name' => 'web',
                ],

                [
                    'name' => "create applied job",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit applied job",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete applied job",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "show applied job",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "show applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "move applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage interview",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create interview",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit interview",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete interview",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "show interview",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create archive applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage archive applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete archive applicant",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage employee",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create employee",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete employee",
                    'guard_name' => 'web',
                ],


                [
                    'name' => "manage category",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create category",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit category",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete category",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage location",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create location",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit location",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete location",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage stage",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create stage",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit stage",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete stage",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage skill",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create skill",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit skill",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete skill",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "create question",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit question",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "manage question",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete question",
                    'guard_name' => 'web',
                ],


                [
                    'name' => "manage type",
                    'guard_name' => 'web',
                ],

                [
                    'name' => "create type",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "edit type",
                    'guard_name' => 'web',
                ],
                [
                    'name' => "delete type",
                    'guard_name' => 'web',
                ],

            ];
            Permission::insert($allPermission);



            // Default Owner Role
            $ownerRoleData = [
                'name' => 'owner',
                'parent_id' => 0,
            ];
            $systemOwnerRole = Role::create($ownerRoleData);

            // Default Owner All Permissions
            $systemOwnerPermission = [
                ['name' => 'manage user'],
                ['name' => 'create user'],
                ['name' => 'edit user'],
                ['name' => 'delete user'],
                ['name' => 'manage role'],
                ['name' => 'create role'],
                ['name' => 'edit role'],
                ['name' => 'delete role'],
                ['name' => 'manage contact'],
                ['name' => 'create contact'],
                ['name' => 'edit contact'],
                ['name' => 'delete contact'],
                ['name' => 'manage note'],
                ['name' => 'create note'],
                ['name' => 'edit note'],
                ['name' => 'delete note'],
                ['name' => 'manage logged history'],
                ['name' => 'delete logged history'],
                ['name' => 'manage pricing packages'],
                ['name' => 'buy pricing packages'],
                ['name' => 'manage pricing transation'],
                ['name' => 'manage account settings'],
                ['name' => 'manage password settings'],
                ['name' => 'manage general settings'],
                ['name' => 'manage company settings'],
                ['name' => 'manage email settings'],
                ['name' => 'manage notification'],
                ['name' => 'edit notification'],
                ['name' => 'manage 2FA settings'],
                ['name' => 'manage seo settings'],
                ['name' => 'manage google recaptcha settings'],
                ['name' => 'manage FAQ'],
                ['name' => 'create FAQ'],
                ['name' => 'edit FAQ'],
                ['name' => 'delete FAQ'],
                ['name' => 'manage Page'],
                ['name' => 'create Page'],
                ['name' => 'edit Page'],
                ['name' => 'delete Page'],
                ['name' => 'show Page'],
                ['name' => 'manage home page'],
                ['name' => 'edit home page'],
                ['name' => 'manage footer'],
                ['name' => 'edit footer'],
                ['name' => 'manage auth page'],

                ['name' => "manage category"],
                ['name' => "create category"],
                ['name' => "edit category"],
                ['name' => "delete category"],
                ['name' => "manage location"],
                ['name' => "create location"],
                ['name' => "edit location"],
                ['name' => "delete location"],
                ['name' => "manage stage"],
                ['name' => "create stage"],
                ['name' => "edit stage"],
                ['name' => "delete stage"],
                ['name' => "manage skill"],
                ['name' => "create skill"],
                ['name' => "edit skill"],
                ['name' => "delete skill"],
                ['name' => "create question"],
                ['name' => "edit question"],
                ['name' => "manage question"],
                ['name' => "delete question"],
                ['name' => "manage applied job"],
                ['name' => "create applied job"],
                ['name' => "edit applied job"],
                ['name' => "delete applied job"],
                ['name' => "show applied job"],
                ['name' => "manage applicant"],
                ['name' => "create applicant"],
                ['name' => "edit applicant"],
                ['name' => "delete applicant"],
                ['name' => "show applicant"],
                ['name' => "move applicant"],
                ['name' => "create type"],
                ['name' => "manage type"],
                ['name' => "edit type"],
                ['name' => "delete type"],
                ['name' => "manage interview"],
                ['name' => "create interview"],
                ['name' => "edit interview"],
                ['name' => "delete interview"],
                ['name' => "show interview"],
                ['name' => "create archive applicant"],
                ['name' => "manage archive applicant"],
                ['name' => "delete archive applicant"],
                ['name' => "manage employee"],
                ['name' => "create employee"],
                ['name' => "delete employee"],


            ];
            $systemOwnerRole->givePermissionTo($systemOwnerPermission);

            // Default Owner Create
            $ownerData =    [
                'name' => 'Owner',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456'),
                'type' => 'owner',
                'lang' => 'english',
                'email_verified_at' => now(),
                'profile' => 'avatar.png',
                'parent_id' => 0,
            ];
            $systemOwner = User::create($ownerData);
            // Default Template Assign
            defaultTemplate($systemOwner->id);
            // Default Owner Role Assign
            $systemOwner->assignRole($systemOwnerRole);

            HomePageSection();
            CustomPage();
            authPage($systemOwnerRole->id);
            DefaultBankTransferPayment();


            // Default Owner Role
            $managerRoleData =  [
                'name' => 'manager',
                'parent_id' => $systemOwner->id,
            ];
            $systemManagerRole = Role::create($managerRoleData);
            // Default Manager All Permissions
            $systemManagerPermission = [
                ['name' => 'manage user'],
                ['name' => 'create user'],
                ['name' => 'edit user'],
                ['name' => 'delete user'],
                ['name' => 'manage contact'],
                ['name' => 'create contact'],
                ['name' => 'edit contact'],
                ['name' => 'delete contact'],
                ['name' => 'manage note'],
                ['name' => 'create note'],
                ['name' => 'edit note'],
                ['name' => 'delete note'],
                ['name' => 'manage 2FA settings'],

                ['name' => "create question"],
                ['name' => "edit question"],
                ['name' => "manage question"],
                ['name' => "delete question"],
                ['name' => "manage applied job"],
                ['name' => "create applied job"],
                ['name' => "edit applied job"],
                ['name' => "delete applied job"],
                ['name' => "show applied job"],
                ['name' => "manage applicant"],
                ['name' => "create applicant"],
                ['name' => "edit applicant"],
                ['name' => "delete applicant"],
                ['name' => "show applicant"],
                ['name' => "move applicant"],
                ['name' => "create interview"],
                ['name' => "edit interview"],
                ['name' => "delete interview"],
                ['name' => "show interview"],
                ['name' => "create archive applicant"],
                ['name' => "manage archive applicant"],
                ['name' => "delete archive applicant"],
                ['name' => "manage employee"],
                ['name' => "create employee"],
                ['name' => "delete employee"],

            ];
            $systemManagerRole->givePermissionTo($systemManagerPermission);

            // Default Manager Create
            $managerData =   [
                'name' => 'Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456'),
                'type' => 'manager',
                'lang' => 'english',
                'email_verified_at' => now(),
                'profile' => 'avatar.png',

                'parent_id' => $systemOwner->id,
            ];
            $systemManager = User::create($managerData);
            // Default Manager Role Assign
            $systemManager->assignRole($systemManagerRole);




        }
    }
}
