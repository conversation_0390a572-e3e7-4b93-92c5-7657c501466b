<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserGroup extends Model
{
    use SoftDeletes;

    // 允许批量赋值的字段
    protected $fillable = [
        'name', 'description', 'created_by', 'updated_by'
    ];

    /**
     * 获取用户组的所有成员
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function members()
    {
        // 关联user_group_members表
        return $this->hasMany(UserGroupMember::class, 'group_id', 'id');
    }

    /**
     * 获取用户组的用户（通过中间表）
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users()
    {
        // 通过user_group_members表建立多对多关系
        return $this->belongsToMany(User::class, 'user_group_members', 'group_id', 'user_id')
            ->withTimestamps()
            ->withPivot('deleted_at')
            ->wherePivotNull('deleted_at'); // 只获取未软删除的成员
    }
} 