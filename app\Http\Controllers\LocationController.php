<?php

namespace App\Http\Controllers;

use App\Models\Location;
use Illuminate\Http\Request;

class LocationController extends Controller
{

    public function index()
    {
        if(\Auth::user()->can('manage location'))
        {
            $locations = Location::where('parent_id', parentId())->get();
            return view('location.index', compact('locations'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }

    public function create()
    {
        return view('location.create');
    }


    public function store(Request $request)
    {
        if(\Auth::user()->can('create location'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'location' => 'required',
                    'state' => 'required',
                    'country' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $location            = new Location();
            $location->location  = $request->location;
            $location->state     = $request->state;
            $location->country   = $request->country;
            $location->parent_id = parentId();
            $location->save();

            return redirect()->back()->with('success', __('Location successfully created.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function show(Location $location)
    {
        //
    }


    public function edit(Location $location)
    {
        return view('location.edit', compact('location'));
    }


    public function update(Request $request, Location $location)
    {
        if(\Auth::user()->can('edit location'))
        {
            $validator = \Validator::make(
                $request->all(), [
                    'location' => 'required',
                    'state' => 'required',
                    'country' => 'required',
                ]
            );
            if($validator->fails())
            {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $location->location  = $request->location;
            $location->state     = $request->state;
            $location->country   = $request->country;
            $location->save();
            return redirect()->back()->with('success', __('Location successfully updated.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function destroy(Location $location)
    {
        if(\Auth::user()->can('delete location'))
        {
            $location->delete();
            return redirect()->back()->with('success', 'Location successfully deleted.');
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }
}
