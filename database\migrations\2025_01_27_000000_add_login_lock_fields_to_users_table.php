<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // 登录失败次数
            $table->integer('login_failed_attempts')->default(0)->after('google_id')->comment('登录失败次数');
            // 账户锁定时间
            $table->timestamp('locked_at')->nullable()->after('login_failed_attempts')->comment('账户锁定时间');
            // 锁定原因
            $table->string('lock_reason')->nullable()->after('locked_at')->comment('锁定原因');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['login_failed_attempts', 'locked_at', 'lock_reason']);
        });
    }
}; 