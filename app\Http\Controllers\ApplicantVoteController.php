<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\ApplicantVote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApplicantVoteController extends Controller
{
    /**
     * 验证用户是否有权限进行投票操作
     * 只有manager和hod角色可以进行投票
     */
    private function checkVotePermission()
    {
        $user = Auth::user();
        if (!$user || !ApplicantVote::canVote($user)) {
            return false;
        }
        return true;
    }
    
    /**
     * 显示申请人的投票情况
     *
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function showVotes($applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 获取该申请人的所有投票
        $votes = $applicant->votes()->with('user')->get();
        
        // 获取投票统计
        $voteStats = $applicant->voteStats();
        
        // 获取当前用户的投票（如果存在）
        $userVote = $applicant->getUserVote(Auth::id());
        
        return view('applicant.votes.index', compact('applicant', 'votes', 'voteStats', 'userVote'));
    }
    
    /**
     * 显示投票表单
     *
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function create($applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 检查用户是否已经投过票
        $existingVote = $applicant->getUserVote(Auth::id());
        
        return view('applicant.votes.create', compact('applicant', 'existingVote'));
    }
    
    /**
     * 处理投票提交
     *
     * @param \Illuminate\Http\Request $request
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 验证表单输入
        $validator = Validator::make($request->all(), [
            'vote_value' => 'required|integer|in:-1,0,1',
            'comment' => 'nullable|string|max:500',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        // 查找现有投票或创建新投票
        $vote = ApplicantVote::updateOrCreate(
            [
                'applicant_id' => $applicantId,
                'user_id' => Auth::id()
            ],
            [
                'vote_value' => $request->vote_value,
                'comment' => $request->comment
            ]
        );
        
        return redirect()->route('applicant.votes.show', $applicantId)
            ->with('success', __('Your vote has been recorded.'));
    }
    
    /**
     * 显示编辑投票表单
     *
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function edit($applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 获取当前用户的投票
        $vote = $applicant->getUserVote(Auth::id());
        if (!$vote) {
            return redirect()->route('applicant.votes.create', $applicantId)
                ->with('info', __('You have not voted for this applicant yet.'));
        }
        
        return view('applicant.votes.edit', compact('applicant', 'vote'));
    }
    
    /**
     * 更新投票
     *
     * @param \Illuminate\Http\Request $request
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 验证表单输入
        $validator = Validator::make($request->all(), [
            'vote_value' => 'required|integer|in:-1,0,1',
            'comment' => 'nullable|string|max:500',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        // 获取并更新投票
        $vote = $applicant->getUserVote(Auth::id());
        if (!$vote) {
            return redirect()->route('applicant.votes.create', $applicantId)
                ->with('info', __('You have not voted for this applicant yet.'));
        }
        
        $vote->update([
            'vote_value' => $request->vote_value,
            'comment' => $request->comment
        ]);
        
        return redirect()->route('applicant.votes.show', $applicantId)
            ->with('success', __('Your vote has been updated.'));
    }
    
    /**
     * 删除投票
     *
     * @param int $applicantId 申请人ID
     * @return \Illuminate\Http\Response
     */
    public function destroy($applicantId)
    {
        // 检查权限
        if (!$this->checkVotePermission()) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        
        // 获取申请人信息
        $applicant = Applicant::find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', __('Applicant not found.'));
        }
        
        // 删除投票
        $vote = $applicant->getUserVote(Auth::id());
        if ($vote) {
            $vote->delete();
            return redirect()->back()->with('success', __('Your vote has been removed.'));
        }
        
        return redirect()->back()->with('info', __('No vote found to remove.'));
    }
}
