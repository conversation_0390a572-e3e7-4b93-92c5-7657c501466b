<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class XSS
{
    /**
     * 处理传入请求，设置语言和时区，进行XSS过滤
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(\Auth::check())
        {
            \App::setLocale(\Auth::user()->lang);
            $timezone= getSettingsValByName('timezone');
            \Config::set('app.timezone', $timezone);
        }

        $data = $request->all();
        array_walk_recursive(
            $data, function (&$data){
            // $data = strip_tags($data);
        }
        );
        $request->merge($data);
        return $next($request);
    }
}
