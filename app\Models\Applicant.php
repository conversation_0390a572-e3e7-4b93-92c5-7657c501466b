<?php

/**
 * Applicant 模型辅助方法声明
 *
 * @method static \Illuminate\Database\Eloquent\Builder|static assignedJobAccess()
 * @method bool can(string $permission)
 * @method bool hasRole(string|array $roles)
 * @method \Illuminate\Database\Eloquent\Relations\BelongsToMany assignedJobs()
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    use HasFactory;
    protected $fillable = [
        'job',
        'name',
        'email',
        'phone',
        'dob',
        'gender',
        'address',
        'profile',
        'resume',
        'experience',
        'expected_salary',
        'cover_letter',
        'stage',
        'order',
        'skill',
        'rating',
        'is_archive',
        'is_hire',
        'joining_date',
        'ending_date',
        'question',
        'parent_id',
        'ext_data',
    ];

    public function appliedJob()
    {
        return $this->hasOne('App\Models\AppliedJob', 'id', 'job');
    }

    public function totalComment()
    {
        $comment = ApplicantComment::where('applicant', $this->id)->count();

        return $comment;
    }

    public function stages()
    {
        return $this->hasOne('App\Models\Stage', 'id', 'stage');
    }

    /**
     * 获取申请人的所有投票
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function votes()
    {
        return $this->hasMany(ApplicantVote::class, 'applicant_id');
    }

    /**
     * 获取申请人的投票统计数据
     *
     * @return array
     */
    public function voteStats()
    {
        return ApplicantVote::getVoteStats($this->id);
    }

    /**
     * 判断指定用户是否已经对该申请人投过票
     *
     * @param int $userId
     * @return bool
     */
    public function hasVoteFromUser($userId)
    {
        return $this->votes()->where('user_id', $userId)->exists();
    }

    /**
     * 获取指定用户对申请人的投票
     *
     * @param int $userId
     * @return ApplicantVote|null
     */
    public function getUserVote($userId)
    {
        return $this->votes()->where('user_id', $userId)->first();
    }

    public static $gender=[
        'Male'=>'Male',
        'Female'=>'Female',
    ];

    protected static function booted()
    {
        parent::booted();

        // 为 HOD 或无"manage applicant"权限的用户添加全局作用域
        static::addGlobalScope('assigned_job_access', function (\Illuminate\Database\Eloquent\Builder $builder) {
            // 仅在用户登录状态下才进行过滤
            if (auth()->check()) {
                $user = auth()->user();

                // 若用户是 HOD，则始终按照分配职位过滤（即便拥有 manage applicant 权限）
                if (method_exists($user, 'hasRole') && call_user_func([$user, 'hasRole'], 'hod')) {
                    // 获取当前用户被分配的职位 ID 集合
                    $jobIds = collect();
                    if (method_exists($user, 'assignedJobs')) {
                        $assignedJobsRelation = call_user_func([$user, 'assignedJobs']);
                        $jobIds = $assignedJobsRelation->pluck('applied_jobs.id');
                    }

                    if ($jobIds->isNotEmpty()) {
                        // 仅查询与用户关联的职位下的申请者
                        $builder->whereIn('job', $jobIds);
                    } else {
                        // 如果没有任何关联职位，保证结果为空
                        $builder->whereRaw('1 = 0');
                    }
                    return; // HOD 处理完毕，后续不再执行
                }

                // 对于非 HOD 且没有 manage applicant 权限的普通用户，同样进行职位过滤
                if (!(method_exists($user, 'can') && call_user_func([$user, 'can'], 'manage applicant'))) {
                    $jobIds = collect();
                    if (method_exists($user, 'assignedJobs')) {
                        $assignedJobsRelation = call_user_func([$user, 'assignedJobs']);
                        $jobIds = $assignedJobsRelation->pluck('applied_jobs.id');
                    }

                    if ($jobIds->isNotEmpty()) {
                        $builder->whereIn('job', $jobIds);
                    } else {
                        $builder->whereRaw('1 = 0');
                    }
                }
            }
        });
    }
}
