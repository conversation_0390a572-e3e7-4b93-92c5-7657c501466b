<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 运行迁移
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_members', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('group_id')->comment('用户组ID');
            $table->timestamps();
            $table->softDeletes()->comment('软删除');
            $table->unique(['user_id', 'group_id'], 'unique_user_group');
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('group_id')->references('id')->on('user_groups')->onDelete('cascade');
        });
    }

    /**
     * 回滚迁移
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_members');
    }
}; 