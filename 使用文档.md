# 登錄jobboard後臺

## 登入系統
1. 打開瀏覽器，輸入https://job.bingo-test.com
2. 輸入用户名稱和密碼，點擊登錄按鈕
3. 成功登錄後，即可開始使用後臺管理系統
> 説明：默認一開始只能通過賬號和密碼登錄，不能通過google登錄。必須登錄后通過綁定google帳號才能使用google登錄。

## 保持長久登錄

Remember me（記住我）功能允許用戶在登錄時選擇保持登錄狀態，即使關閉瀏覽器或計算機後也能保持登錄。當您在公共或共享計算機上使用系統時，請謹慎使用此功能。啟用此功能後，系統會在您的瀏覽器中存儲一個加密的認證令牌，使您在指定時間內無需重新輸入憑據即可訪問系統。這提高了用戶體驗，特別是對於頻繁訪問系統的管理員來說非常方便。

登入時，只需在輸入用戶名和密碼後，勾選「Remember me」複選框，然後點擊登錄按鈕即可。系統將會記住您的登錄狀態一段時間，具體時長取決於系統設置。

請注意：為了安全起見，如果長時間未使用系統，即使啟用了Remember me功能，系統也可能要求重新驗證身份。

請參考如下圖片：

## 忘記密碼怎麽辦？
如果你忘記密碼，請點擊「Forgot Password」按鈕，然後填寫您的用戶名或電郵地址，然後點擊「Send」按鈕。系統將會發送一封包含重置密碼連結的電郵給您。

具體步驟如下：
1. 點擊登錄界面中「Forgot Password」按鈕
2. 跳轉到忘記密碼界面
3. 填寫郵箱地址
4. 點擊「Send Reset Link」按鈕
5. 如果郵件發送成功，則會收到一封包含重置密碼連結的電郵，并且頁面會提示`We have e-mailed your password reset link.`
6. 登錄你的郵箱，點開郵件
7. 點擊 「Reset Password」 按鈕進行密碼重置

## 系統設置
登錄后需要設置一些配置才能讓系統正常工作，其中最重要的是郵件設置。
1. 點擊左側導航列中的「Settings」
2. 選擇「Email」
4. 輸入發送者名字，也就是Sender Name
5. 輸入發送者郵箱，也就是Sender Email
6. 輸入郵件驅動，也就是SMTP Driver
7. 輸入郵件服務器地址，也就是SMTP Host
8. 輸入郵件服務器端口，也就是SMTP Port
9. 輸入郵件服務器用户名稱，也就是SMTP Username
10. 輸入郵件服務器密碼，也就是SMTP Password
11. 輸入郵件服務器加密方式，也就是SMTP Encryption
11. 點擊「Test Email」按鈕會彈出一個窗口，輸入Test Email
12. 點擊「Send Mail」按鈕發送郵件
13. 如果郵件發送成功，可以點擊「Save」按鈕保存設置
請參考如下圖：
![Email設置](/assets/images/email.png)

## 綁定Google帳號
1. 點擊左側導航列中的「Settings」
2. 選擇「User Profile」
3. 點擊「Bind Google Account」按鈕
4. 此時會打開瀏覽器，讓你選擇你需要綁定的Google帳號
5. 如果綁定成功，會返回到首頁

>注意：當前系統賬號的郵箱必須和google賬號綁定的郵箱一致

請參考如下圖：
![Google設置](/assets/images/google.png)


# 用戶管理

用戶管理功能是整個系統的核心部分之一，允許管理員對系統用戶進行全面管理。通過這個模塊，您可以添加新用戶、分配不同角色和權限、編輯用戶信息、刪除用戶以及管理用戶的訪問權限。系統支持多種用戶角色，每個角色可以有不同的權限設置，確保系統安全和職責分離。此外，用戶可以綁定Google帳號實現雙重認證，提高帳戶安全性。

## 添加用戶
登錄后需要管理用戶，可以添加、編輯、刪除用戶。這樣系統才能正常運作。
1. 點擊左側導航列中的「Staff Management」展開菜單
2. 選擇「Users」
3. 點擊「Create User」按鈕，顯示新增用戶窗口
4. 在Assign Role選項中選擇用戶的角色
5. 在Name輸入框中輸入用戶名稱
7. 在Email輸入框中輸入用戶郵箱
8. 在Password輸入框中輸入用戶密碼
9. 在Phone Number輸入框中輸入用戶電話號碼
10. 點擊Profile輸入框選擇上傳用戶的頭像
11. 點擊「Create」按鈕保存用戶信息

請參考如下圖片：
![添加用戶](/assets/images/add-user.png)

## 刪除用戶
如果需刪除用戶，請點擊用戶列表中的刪除按鈕。
具體步驟如下：
1. 點擊用戶列表中的刪除按鈕
2. 顯示確認刪除對話框
3. 點擊確認按鈕，刪除成功
4. 點擊取消按鈕，取消刪除

請參考如下圖片：

## 修改用戶
如果需修改用戶，請點擊用戶列表中的修改按鈕。
具體步驟如下：
1. 點擊用戶列表中的修改按鈕
2. 顯示修改用戶對話框
3. 點擊Assign Role按鈕，選擇角色
4. 在Name輸入框中輸入用戶名
5. 在Email輸入框中輸入用戶Email
6. 在Phone輸入框中輸入用戶Phone
7. 點擊Profile輸入框，選擇用戶頭像
8. 點擊「Update」按鈕，保存修改

請參考如下圖片：

# 用戶組管理

用戶組管理功能允許管理員將用戶組織成不同的組，以便更有效地管理用戶。這簡化了管理過程，特別是在用戶數量較多的情況下。用戶組功能支持創建、編輯和刪除用戶組，並將用戶分配給相應的組。同時用戶組也方便了不同角色的用戶對崗位申請人進行訪問控制。

## 添加用戶組
添加用戶組功能允许管理员创建新的用户组，具體步驟為：
1. 點擊「Create Group」按鈕
2. 顯示新增用戶組的表單窗口
3. 在Group Name字段中輸入用戶組名
4. 在Description字段中輸入用戶組描述,Description字段可選填
5. 點擊「Create」按鈕保存新增的用戶組信息
6. 如果不想保存新增的用戶組信息，可以點擊「Cancel」按鈕取消新增

請參考如下圖片：

## 編輯用戶組
編輯用戶組功能允许管理员修改已有的用户组信息，具體步驟為：
1. 點擊用戶組列表中的修改按鈕
2. 顯示修改用戶組的表單窗口
3. 在Group Name字段中修改用戶組名
4. 在Description字段中修改用戶組描述
5. 點擊「Save Changed」按鈕保存修改
6. 如果不想保存修改，可以點擊「Cancel」按鈕取消修改

請參考如下圖片：

## 刪除用戶組
刪除用戶組功能允许管理员删除已有的用户组，具體步驟為：
1. 點擊用戶組列表中的刪除按鈕
2. 顯示確認刪除對話框
3. 點擊確認按鈕，刪除成功
4. 點擊取消按鈕，取消刪除

請參考如下圖片：

## 管理成員
管理成員功能允许管理员将用户添加到用户组中，具體步驟為：
1. 點擊用戶組列表中的管理成員按鈕
2. 顯示管理成員的表單窗口
3. 在下拉框中選擇需要添加的成員
4. 點擊下拉框旁邊的Add按鈕添加成員
5. 如果添加成功，在下拉框下面的列表中會出現添加的成員
6. 如果想刪除添加的成員，可以點擊列表中的刪除按鈕
7. 最後點擊close按鈕關閉窗口

請參考如下圖片：

# 給角色分配合理的權限
管理員需要為為不同角色分配合理的權限，這樣不同角色賬號才能正常工作,具體步驟為：
1. 點擊左側導航列中的Staff Management中的Roles
2. 選擇需要編輯的角色
3. 顯示權限編輯窗口
4. 在權限編輯窗口中，展示了所有的權限
5. 點擊權限編輯窗口中的checkbox，選擇需要給角色的權限
6. 點擊Update 按鈕保存權限編輯

請參考如下圖片：

# 職責介紹

經理的職責包括但不限於：負責創建新的崗位，設置崗位需求，並根據部門需求安排面試流程；審查所有申請人的資料，確保申請信息的真實性和完整性；審閱HOD（部門負責人）提交的審批投票結果和意見，綜合各方反饋，對申請人進行全面評估。經理需根據崗位要求和面試表現，最終決定申請人是否進入下一個招聘階段，或是否被錄取。經理還需協調各部門之間的溝通，確保招聘流程的順利進行，並對招聘結果負責。

# 初始化設置

## 設置面試階段
創建崗位功能前，需要設置面試階段，這樣才能在創建崗位時選擇面試階段。具體步驟為：
1. 點擊左側導航列中的 System Configuration中的Stages
2. 點擊「Create Stage」按鈕，顯示新增面試階
3. 在Stage輸入框中輸入面試階段名稱
4. 點擊Create按鈕保存新增的面試階段信息

如果想修改面試階段，可以點擊面試階段列表中的修改按鈕，然後在Stage輸入框中修改面試階段名稱，最後點擊Update按鈕保存修改。

如果想刪除面試階段，可以點擊面試階段列表中的刪除按鈕，然後在確認刪除對話框中點擊確認按鈕，刪除成功。

請參考如下圖片：
![新增面試階段](/assets/images/stage.png)

## 設置面試類型
面試類型的作用是對工作崗位進行分類，設置面試類型功能允许管理员创建新的面試類型，具體步驟為：
1. 點擊左側導航列中的 System Configuration中的Type
2. 點擊「Create Type」按鈕，顯示新增面試類型的表單窗口
3. 在Type字段中輸入面試類型名稱
4. 點擊Create按鈕保存新增的面試類型信息

如果想修改面試類型，可以點擊面試類型列表中的修改按鈕，然後在Type輸入框中修改面試類型名稱，最後點擊Update按鈕保存修改。

如果想刪除面試類型，可以點擊面試類型列表中的刪除按鈕，然後在確認刪除對話框中點擊確認按鈕，刪除成功。

請參考如下圖片：
![新增面試類型](/assets/images/type.png)

## 設置Location
Location的作用是工作崗位對應的地點，設置Location功能允许管理员创建新的面試地點，具體步驟為：
1. 點擊左側導航列中的 System Configuration中的Location
2. 點擊「Create Location」按鈕，顯示新增面試地點的表單窗口
3. 在Location字段中輸入面試地點名稱
4. 在State字段中輸入面試地點所在州
5. 在Country字段中輸入面試地點所在國家
4. 點擊Create按鈕保存新增的面試地點信息

如果想修改面試地點，可以點擊面試地點列表中的修改按鈕，然後在Location輸入框中修改面試地點名稱，在State輸入框中修改面試地點所在州，在Country輸入框中修改面試地點所在國家，最後點擊Update按鈕保存修改。

如果想刪除面試地點，可以點擊面試地點列表中的刪除按鈕，然後在確認刪除對話框中點擊確認按鈕，刪除成功。

請參考如下圖片：
![新增面試地點](/assets/images/location.png)

## 設置Skill
Skill的作用是工作崗位所需的技能，設置Skill功能允许管理员创建新的技能，具體步驟為：
1. 點擊左側導航列中的 System Configuration中的Skill
2. 點擊「Create Skill」按鈕，顯示新增技能的表單窗口
3. 在Skill字段中輸入技能名稱
4. 點擊Create按鈕保存新增的技能信息

如果想修改技能，可以點擊技能列表中的修改按鈕，然後在Skill輸入框中修改技能名稱，最後點擊Update按鈕保存修改。

如果想刪除技能，可以點擊技能列表中的刪除按鈕，然後在確認刪除對話框中點擊確認按鈕，刪除成功。

請參考如下圖片：
![新增技能](/assets/images/skill.png)

## 設置Question
Question的作用是面試申請中的提問，設置Question功能允许管理员创建新的面試問題，具體步驟為：
1. 點擊左側導航列中的 System Configuration中的Question
2. 點擊「Create Question」按鈕，顯示新增面試問題的表單窗口
3. 在Question字段中輸入面試問題名稱
4. 在Required status字段中選擇是否必填
5. 點擊Create按鈕保存新增的面試問題信息

如果想修改面試問題，可以點擊面試問題列表中的修改按鈕，然後在Question輸入框中修改面試問題名稱，在Required status下拉框中修改是否必填，最後點擊Update按鈕保存修改。

如果想刪除面試問題，可以點擊面試問題列表中的刪除按鈕，然後在確認刪除對話框中點擊確認按鈕，刪除成功。

請參考如下圖片：
![新增面試問題](/assets/images/question.png)

## 崗位管理
崗位管理功能是整個系統的核心部分，通過這個模塊，您可以添加、編輯、刪除崗位。系統支持多種崗位，每個崗位可以設置權限和職責。

## 創建崗位
創建崗位功能允许管理员创建新的崗位，具體步驟為：
1. 點擊Ceate Applied Job按鈕
2. 顯示新增崗位的表單窗口
3. 在Title字段中輸入崗位名稱
4. 在Job Code字段中輸入崗位代碼
5. 在Category字段中選擇崗位類別
6. 在Type字段中選擇崗位類型
7. 在Location字段中選擇崗位地點
8. 在Skill字段中選擇崗位技能
9. 在Start Date字段中選擇崗位開始時間
10. 在Due Date字段中選擇崗位結束時間
11. 在Minimum Experience字段中輸入崗位所需經驗
12. 在Maximum Experience字段中輸入崗位所需最大經驗
13. 在Minimum Salary字段中輸入崗位最低薪水
14. 在Maximum Salary字段中輸入崗位最高薪水
15. 在Salary Period字段中選擇薪水周期
16. 在Status字段中選擇崗位狀態
17. 在Assign To User group字段中選擇崗位分配的用戶組
18. 在Assign To Specific User字段中選擇崗位分配的用戶
19. 在Question List字段中選擇崗位相關的問題
20. 在Description字段中輸入崗位描述
21. 在Requirements字段中輸入崗位要求
19. 點擊Create按鈕保存新增的崗位信息

> 说明 ：崗位的Category、Type、Location、Skill、Question List等字段需要在系統配置中先設置好，否則無法選擇。
> 说明：崗位的Assign To User group和Assign To Specific User字段需要在用戶組管理中先設置好，否則無法選擇。

Assign To User group和Assign To Specific User字段是用來指定崗位分配給哪個用戶組的，這樣可以给分配用戶設置當前工作的申請人投票查看權和投票權限。

## 編輯崗位
編輯崗位功能允许管理员修改已有的崗位信息，具體步驟為：
1. 點擊崗位列表中的修改按鈕
2. 顯示編輯崗位的表單窗口
3. 在各個字段中修改崗位信息
4. 點擊Update按鈕保存修改
5. 如果不想保存修改，可以點擊Cancel按鈕取消修改

請參考如下圖片：
![編輯崗位](/assets/images/edit-job.png)

## 刪除崗位
刪除崗位功能允许管理员删除已有的崗位，具體步驟為：
1. 點擊崗位列表中的刪除按鈕
2. 顯示確認刪除對話框
3. 點擊確認按鈕，刪除成功
4. 點擊取消按鈕，取消刪除

請參考如下圖片：
![刪除崗位](/assets/images/delete-job.png)

## 查看崗位詳情
查看崗位詳情功能允许管理员查看已有的崗位信息，具體步驟為：
1. 點擊崗位列表中的查看詳情按鈕
2. 顯示崗位詳情的頁面
3. 在各個字段中查看崗位信息

請參考如下圖片：
![查看崗位詳情](/assets/images/view-job.png)

# 審查申請人
审查功能允许经理对申请人的信息进行审核，并对其進行批准或拒絕。同时可以查看申请人的面试记录和投票结果。审查功能是整个招聘流程中至关重要的一环，确保了申请人符合岗位要求，并且能够顺利进入下一阶段的面试。

## 查看申請人詳情
查看申請人詳情功能允许经理查看申请人的详细信息，具體步驟為：
1. 點擊申請人列表中的查看詳情按鈕或點擊申請人姓名
2. 顯示申請人詳情的頁面
3. 在各個字段中查看申請人信息

請參考如下圖片：
![查看申請人詳情](/assets/images/view-candidate.png)

## 查看所有投票
查看所有投票功能允许经理查看对申请人的所有投票记录，經理根據根據投票結果進行決策，具體步驟為：
1. 點擊申請人詳情頁面中的View All Votes按鈕
2. 顯示所有投票的列表
3. 在列表中查看各個投票的詳細信息

請參考如下圖片：
![查看所有投票](/assets/images/view-votes.png)

## 進行投票
進行投票功能允许经理对申請人進行投票和評論，具體步驟為：
1. 點擊申請人詳情頁面中的Your Vote複選框中選擇投票結果
2. 在Vote Comment輸入框中輸入評論
3. 點擊Submit Vote按鈕提交投票
4. 如果已經投票了，需要修改投票，可以修改投票信息后，可以點擊Update Vote按鈕來更新投票結果

請參考如下圖片：
![進行審查](/assets/images/review-candidate.png)

## 錄取申請人
錄取申請人功能允许经理对申请人進行錄取，具體步驟為：
1. 點擊申請人詳情頁面中右上角的Add to Eemployee List按鈕，圖標爲一個圓形加號
2. 顯示一個確認對話框
3. 在綫Join Date輸入框中輸入申請人加入公司的日期
4. 在End Date輸入框中輸入申請人離開公司的日期
5. 點擊Add按鈕，將申請人添加到員工列表中

請參考如下圖片：
![錄取申請人](/assets/images/hire-candidate.png)

## 暫存申請人
如果覺得申請人還需要進一步審查，可以將申請人暫存起來，具體步驟為：
1. 點擊申請人詳情頁面中的Add to Archive List按鈕
2. 跳轉到暫存列表頁面
3. 在暫存列表頁面中可以查看所有暫存的申請

請參考如下圖片：

## 進入下一個面試階段
如果申請人通過了當前的面試階段，可以將申請人移到下一個面試階段，具體步驟為：
1. 在綫申請人列表中，拖動申請人到下一個面試階段

請參考如下圖片：
![進入下一個面試階段](/assets/images/move-to-next-stage.png

## 創建面試
如果申請人進入下一個面試階段，可以創建面試，邀請申請人參加面試，具體步驟為：
1. 左側導航列中點擊All Interview菜單
2. 點擊Create Interview按鈕
3. 顯示新增面試的表單窗口
4. 在Applicant下拉框中選擇申請人
5. 在Date字段中選擇面試日期
6. 在Start Time字段中選擇面試開始時間
7. 在Total Duration字段中輸入面試總時長
8. 在User Assign字段中選擇面試官
9. 在Note字段中輸入面試備註
10. 點擊Create按鈕保存新增的面試信息

> 説明：如果面試創建成功后，系統會自動發送郵件給申請人和面試官，通知他們面試的時間和地點。
> 注意：管理員需要提前設置好郵件服務器，同時設置好郵件模板，這樣才能保證郵件的正常發送。

請參考如下圖片：
![創建面試](/assets/images/create-interview.png)