<?php

namespace App\Http\Controllers;

use App\Models\UserGroup;
use App\Services\UserGroupService;
use Illuminate\Http\Request;

class UserGroupController extends Controller
{
    protected $service;

    /**
     * 构造函数，注入服务
     */
    public function __construct(UserGroupService $service)
    {
        $this->service = $service;
    }

    /**
     * 用户组列表页（Blade）
     */
    public function index()
    {
        $this->authorize('manage-user-group');
        $groups = UserGroup::withCount('users')->get();
        return view('user_group.index', compact('groups'));
    }

    /**
     * 创建用户组表单页（弹窗）
     */
    public function create()
    {
        $this->authorize('manage-user-group');
        return view('user_group.form');
    }

    /**
     * 编辑用户组表单页（弹窗）
     */
    public function edit($id)
    {
        $this->authorize('manage-user-group');
        $group = UserGroup::findOrFail($id);
        return view('user_group.form', compact('group'));
    }

    /**
     * 成员管理页（弹窗）
     */
    public function members($id)
    {
        $this->authorize('manage-group-member');
        $group = UserGroup::findOrFail($id);
        $members = $group->members()->with('user')->get();
        $allUsers = \App\Models\User::where('is_active', 1)->get();
        return view('user_group.members', compact('group', 'members', 'allUsers'));
    }

    /**
     * 创建用户组（表单提交）
     */
    public function store(Request $request)
    {
        $this->authorize('manage-user-group');
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
        ]);
        $data['created_by'] = auth()->id();
        $this->service->createGroup($data);
        return redirect()->route('user-groups.index')->with('success', 'group create success');
    }

    /**
     * 更新用户组（表单提交）
     */
    public function update(Request $request, $id)
    {
        $this->authorize('manage-user-group');
        $group = UserGroup::findOrFail($id);
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
        ]);
        $data['updated_by'] = auth()->id();
        $this->service->updateGroup($group, $data);
        return redirect()->route('user-groups.index')->with('success', 'group update success');
    }

    /**
     * 删除用户组（软删除）
     */
    public function destroy($id)
    {
        $this->authorize('manage-user-group');
        $group = UserGroup::findOrFail($id);
        $this->service->deleteGroup($group);
        return redirect()->route('user-groups.index')->with('success', 'group delete success');
    }

    /**
     * 恢复软删除的用户组
     */
    public function restore($id)
    {
        $this->authorize('manage-user-group');
        $result = $this->service->restoreGroup($id);
        if ($result) {
            return redirect()->route('user-groups.index')->with('success', 'group restore success');
        }
        return redirect()->route('user-groups.index')->with('error', 'group not found');
    }
} 