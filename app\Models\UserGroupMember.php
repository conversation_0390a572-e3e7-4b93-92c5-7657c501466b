<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserGroupMember extends Model
{
    use SoftDeletes;

    // 允许批量赋值的字段
    protected $fillable = [
        'user_id', 'group_id'
    ];

    /**
     * 获取成员对应的用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取成员对应的用户组
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function group()
    {
        return $this->belongsTo(UserGroup::class, 'group_id', 'id');
    }
} 