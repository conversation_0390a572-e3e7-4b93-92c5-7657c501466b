<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 运行迁移
     * @return void
     */
    public function up()
    {
        Schema::create('user_groups', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->comment('用户组名称');
            $table->string('description')->nullable()->comment('用户组描述');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新者ID');
            $table->timestamps();
            $table->softDeletes()->comment('软删除');
        });
    }

    /**
     * 回滚迁移
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_groups');
    }
}; 