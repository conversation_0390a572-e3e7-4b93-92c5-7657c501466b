<?php

namespace App\Http\Controllers;

use App\Models\Stage;
use Illuminate\Http\Request;

class StageController extends Controller
{

    public function index()
    {
        if (\Auth::user()->can('manage stage')) {
            $stages = Stage::where('parent_id', parentId())->orderBy('order', 'asc')->get();
            return view('stage.index', compact('stages'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function create()
    {
        return view('stage.create');
    }


    public function store(Request $request)
    {
        if (\Auth::user()->can('create stage')) {
            $validator = \Validator::make(
                $request->all(), [
                    'stage' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $latestStage = Stage::orderBy('order', 'desc')->first();
            $stage = new Stage();
            $stage->stage = $request->stage;
            $stage->order = !empty($latestStage) ? $latestStage->order + 1 : 0;
            $stage->parent_id = parentId();
            $stage->save();

            return redirect()->back()->with('success', __('Stage successfully created.'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function show(Stage $stage)
    {
        //
    }


    public function edit(Stage $stage)
    {
        return view('stage.edit', compact('stage'));
    }


    public function update(Request $request, Stage $stage)
    {
        if (\Auth::user()->can('edit stage')) {
            $validator = \Validator::make(
                $request->all(), [
                    'stage' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }
            $stage->stage = $request->stage;
            $stage->save();
            return redirect()->back()->with('success', __('Stage successfully updated.'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }


    public function destroy(Stage $stage)
    {
        if (\Auth::user()->can('delete stage')) {
            $stage->delete();
            return redirect()->back()->with('success', 'Stage successfully deleted.');
        } else {
            return redirect()->back()->with('error', __('Permission Denied!'));
        }
    }

    public function stageOrder(Request $request)
    {
        $stageData = $request->all();
        foreach ($stageData['rowOrder'] as $key => $val) {
            $stage = Stage::where('id', $val)->first();
            $stage->order = $key;
            $stage->save();
        }
        return json_encode(
            [
                'success'=>__('Stage order succcessfylly changed.'),
            ]
        );
    }
}
