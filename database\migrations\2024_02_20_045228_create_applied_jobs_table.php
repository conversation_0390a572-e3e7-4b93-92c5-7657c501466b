<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applied_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('job_title')->nullable();
            $table->integer('job_location')->default(0);
            $table->integer('job_category')->default(0);
            $table->string('min_experience')->nullable();
            $table->string('max_experience')->nullable();
            $table->integer('min_salary')->nullable();
            $table->integer('max_salary')->nullable();
            $table->string('salary_period')->nullable();
            $table->string('job_type')->nullable();
            $table->string('job_skill')->nullable();
            $table->integer('position')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('status')->nullable();
            $table->string('code')->nullable();
            $table->string('question')->nullable();
            $table->text('job_description')->nullable();
            $table->text('job_requirement')->nullable();
            $table->integer('parent_id')->default(0);
            $table->integer('is_active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applied_jobs');
    }
};
