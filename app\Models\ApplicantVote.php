<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApplicantVote extends Model
{
    use HasFactory;
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'applicant_id',
        'user_id',
        'vote_value',
        'comment',
    ];
    
    /**
     * 投票值常量
     */
    const VOTE_SUPPORT = 1;    // 支持
    const VOTE_NEUTRAL = 0;    // 中立
    const VOTE_OPPOSE = -1;    // 反对
    
    /**
     * 获取投票关联的申请人
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function applicant()
    {
        return $this->belongsTo(Applicant::class, 'applicant_id');
    }
    
    /**
     * 获取投票的用户
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 检查用户是否有权限投票
     * 只有owner、经理和HOD角色的用户可以投票
     *
     * @param User $user
     * @return bool
     */
    public static function canVote(User $user)
    {
        return $user->hasRole(['owner', 'manager', 'hod']);
    }
    
    /**
     * 获取指定申请人的投票统计
     *
     * @param int $applicantId
     * @return array 包含支持票、反对票和中立票数量的数组
     */
    public static function getVoteStats($applicantId)
    {
        $stats = [
            'support' => self::where('applicant_id', $applicantId)
                             ->where('vote_value', self::VOTE_SUPPORT)
                             ->count(),
            'oppose' => self::where('applicant_id', $applicantId)
                            ->where('vote_value', self::VOTE_OPPOSE)
                            ->count(),
            'neutral' => self::where('applicant_id', $applicantId)
                             ->where('vote_value', self::VOTE_NEUTRAL)
                             ->count(),
            'total' => self::where('applicant_id', $applicantId)->count(),
        ];
        
        // 计算支持率
        $stats['support_rate'] = $stats['total'] > 0 
            ? round(($stats['support'] / $stats['total']) * 100, 2) 
            : 0;
            
        return $stats;
    }
}
