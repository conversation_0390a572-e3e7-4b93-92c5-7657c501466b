<?php

namespace App\Http\Controllers;

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;


class RoleController extends Controller
{

    public function index()
    {
         $roleData = Role::where('parent_id', parentId())->get();
            return view('role.index', compact('roleData'));
    }


    public function create()
    {
        // 检查是否已存在hod角色
        $existingHodRole = Role::where('parent_id', parentId())->where('name', 'hod')->first();
        if ($existingHodRole) {
            return redirect()->route('role.index')->with('error', 'HOD角色已存在，无法重复创建。');
        }

        $permissionList = new Collection();
        foreach (\Auth::user()->roles as $role) {
            $permissionList = $permissionList->merge($role->permissions);
        }
        return view('role.create', compact('permissionList'));
    }


    public function store(Request $request)
    {
        // 验证角色名称，只允许创建hod角色
        if ($request->title !== 'hod') {
            return redirect()->route('role.index')->with('error', '只能创建HOD角色。');
        }

        // 检查是否已存在hod角色
        $existingHodRole = Role::where('parent_id', parentId())->where('name', 'hod')->first();
        if ($existingHodRole) {
            return redirect()->route('role.index')->with('error', 'HOD角色已存在，无法重复创建。');
        }

       $validator = \Validator::make(
                $request->all(), [
                    'title' => 'required|unique:roles,name,null,id,parent_id,' . parentId(),
                    'user_permission' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->route('role.index')->with('error', $messages->first());
            }

            $userRole = new Role();
            $userRole->name = $request->title;
            $userRole->parent_id = parentId();
            $userRole->save();
            foreach ($request->user_permission as $permission) {
                $result = Permission::find($permission);
                $userRole->givePermissionTo($result);
            }
            return redirect()->route('role.index')->with('success', __('Role successfully created.'));

    }


    public function show($id)
    {
        //
    }


    public function edit($id)
    {
        $role = Role::find($id);
        
        // 如果尝试编辑OWNER角色，直接拒绝
        if ($role->name == 'owner') {
            return redirect()->route('role.index')->with('error', 'OWNER角色不可编辑。');
        }
        
        // 如果目标角色是MANAGER，但当前登录用户并非OWNER，则拒绝
        if ($role->name == 'manager' && !\Auth::user()->hasRole('owner')) {
            return redirect()->route('role.index')->with('error', '只有OWNER可以编辑MANAGER角色。');
        }

        $permissionList = new Collection();
        foreach (\Auth::user()->roles as $userRole) {
            $permissionList = $permissionList->merge($userRole->permissions);
        }

        $assignPermission = $role->permissions;
        $assignPermission = $assignPermission->pluck('id')->toArray();

        return view('role.edit', compact('role', 'permissionList', 'assignPermission'));
    }


    public function update(Request $request, $id)
    {
       $userRole = Role::find($id);
       
       // 如果尝试编辑OWNER角色，直接拒绝
       if ($userRole->name == 'owner') {
           return redirect()->route('role.index')->with('error', 'OWNER角色不可编辑。');
       }

       // 如果目标角色是MANAGER，但当前登录用户并非OWNER，则拒绝
       if ($userRole->name == 'manager' && !\Auth::user()->hasRole('owner')) {
           return redirect()->route('role.index')->with('error', '只有OWNER可以编辑MANAGER角色。');
       }

       // 此处不再做统一的系统角色限制，因前方已针对 OWNER / MANAGER 做过权限校验
       // 对于其他角色，例如 HOD，直接继续后续校验逻辑

            $validator = \Validator::make(
                $request->all(), [
                    'title' => 'required|unique:roles,name,' . $userRole->id . ',id,parent_id,' . parentId(),
                    'user_permission' => 'required',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->route('role.index')->with('error', $messages->first());
            }
            $permissionData = $request->except(['permissions']);
            $assignPermissions = $request->user_permission;
            $userRole->fill($permissionData)->save();

            $permissionList = Permission::all();
            foreach ($permissionList as $revokePermission) {
                $userRole->revokePermissionTo($revokePermission);
            }
            foreach ($assignPermissions as $assignPermission) {
                $assign = Permission::find($assignPermission);
                $userRole->givePermissionTo($assign);
            }
            return redirect()->route('role.index')->with('success', __('Role successfully updated.'));

    }


    public function destroy($id)
    {
       $userRole = Role::find($id);
       
       // 禁止删除系统预设角色
       if (in_array($userRole->name, User::$systemRoles)) {
           return redirect()->route('role.index')->with('error', '系统预设角色不可删除。');
       }
       
       // 只允许删除hod角色
       if ($userRole->name !== 'hod') {
           return redirect()->route('role.index')->with('error', '只能删除HOD角色。');
       }

            $userRole->delete();
            return redirect()->route('role.index')->with('success', 'Role successfully deleted.');
    }

}
